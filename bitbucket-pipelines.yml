image: node:18

pipelines:
  custom:
    RollBack-5-Csw:
      - variables:
        - name: VERSION
      - step:
          name: RollBack Csw
          deployment: Csw
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/white-label/extended-pricing-widget"
            - S3_BUCKET="s3://extended-widget-ui-white-label"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_CSW" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_CSW" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-5-Csw:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Csw\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate and Tag
          deployment: Csw
          caches:
            - node
          size: 2x
          script:
            - export NODE_OPTIONS="--max-old-space-size=6144"
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is 
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widget-ui-white-label"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # set renderer version
            - TODAY_DATE_V=$(date +'%d-%b-%y')
            - echo "$TODAY_DATE_V"
            - renderer_version="csw-$TODAY_DATE_V-$build_number"
            - echo "$renderer_version"
            - env_fileName=".env.csw"
            - node ./scripts/update-renderer "$renderer_version" "$env_fileName"
            # start installation and build
            - npm install
            - npm run build:web:csw
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-ui-white-label"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/white-label/extended-pricing-widget"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_CSW" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_CSW" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
            # Notify external service about new version
            - >
              curl --location 'https://cswbryzoswidget.com/widget-service/external-affairs/render/version' --header 'Content-Type: application/json' --data "{\"data\": \"$renderer_version\"}"
            # Create tag
            - TAG_EXISTS=$(git tag -l "csw-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="csw-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "csw-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/csw-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="csw-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Csw\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Csw\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi      

    RollBack-2-Demo:
      - variables:
        - name: VERSION
      - step:
          name: RollBack Demo
          deployment: Demo
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/demo/extended-pricing-widget"
            - S3_BUCKET="s3://extended-widget-ui-demo-one"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-2-Demo:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Demo\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate and Tag
          deployment: Demo
          caches:
            - node
          size: 2x
          script:
            - export NODE_OPTIONS="--max-old-space-size=6144"
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is 
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widget-ui-demo-one"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # set renderer version
            - TODAY_DATE_V=$(date +'%d-%b-%y')
            - echo "$TODAY_DATE_V"
            - renderer_version="demo-$TODAY_DATE_V-$build_number"
            - echo "$renderer_version"
            - env_fileName=".env.demo"
            - node ./scripts/update-renderer "$renderer_version" "$env_fileName"
            # start installation and build
            - npm install
            - npm run build:web:demo
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-ui-demo-one"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/demo/extended-pricing-widget"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_DEMO" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
            # Notify external service about new version
            - >
              curl --location 'https://demobryzoswidget.com/widget-service/external-affairs/render/version' --header 'Content-Type: application/json' --data "{\"data\": \"$renderer_version\"}"
            # Create tag
            - TAG_EXISTS=$(git tag -l "demo-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="demo-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "demo-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/demo-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="demo-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi      

    RollBack-1-QA:
      - variables:
        - name: VERSION
      - step:
          name: RollBack QA
          deployment: QA
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/qa/extended-pricing-widget"
            - S3_BUCKET="s3://extended-widget-ui-qa-one"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"

    Deployment-1-QA:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"QA\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate and Tag
          deployment: QA
          caches:
            - node
          size: 2x
          script:
            - export NODE_OPTIONS="--max-old-space-size=6144"
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widget-ui-qa-one"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # set renderer version
            - TODAY_DATE_V=$(date +'%d-%b-%y')
            - echo "$TODAY_DATE_V"
            - renderer_version="qa-$TODAY_DATE_V-$build_number"
            - echo "$renderer_version"
            - env_fileName=".env.qa"
            - node ./scripts/update-renderer "$renderer_version" "$env_fileName"
            # start installation and build
            - npm install
            - npm run build:web:qa
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-ui-qa-one"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/qa/extended-pricing-widget"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_QA" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else  
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
            # Notify external service about new version
            - >
              curl --location 'https://qabryzoswidget.com/widget-service/external-affairs/render/version' --header 'Content-Type: application/json' --data "{\"data\": \"$renderer_version\"}"
            # Create tag
            - TAG_EXISTS=$(git tag -l "qa-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="qa-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "qa-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/qa-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="qa-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi 

    RollBack-3-Prod:
      - variables:
        - name: VERSION
      - step:
          name: RollBack Prod
          deployment: Production
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/prod/extended-pricing-widget"
            - S3_BUCKET="s3://extended-widget-prod"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-3-Prod:
      - step:
          name: Pipeline Start Notification
          script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"Production\",\"status\":\"${APPROVE}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
      - step:
          name: Build, Upload, Invalidate and Tag
          deployment: Production
          caches:
            - node
          size: 2x
          script:
            - export NODE_OPTIONS="--max-old-space-size=6144"
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
            # Set necessary variables for Current Build
            - S3_BUCKET="s3://extended-widget-prod"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # set renderer version
            - TODAY_DATE_V=$(date +'%d-%b-%y')
            - echo "$TODAY_DATE_V"
            - renderer_version="prod-$TODAY_DATE_V-$build_number"
            - echo "$renderer_version"
            - env_fileName=".env.production"
            - node ./scripts/update-renderer "$renderer_version" "$env_fileName"
            # start installation and build
            - npm install
            - npm run build:web:production
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-prod"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/prod/extended-pricing-widget"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_PROD" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else  
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
            # Notify external service about new version
            - >
              curl --location 'https://prod-extended-widget-service.bryzosservices.com/external-affairs/render/version' --header 'User-Agent: bryzos-ua-41cc2924-27dc-4495-bf47-450a5de0950d' --header 'Content-Type: application/json' --data "{\"data\": \"$renderer_version\"}"
            # Create tag
            - TAG_EXISTS=$(git tag -l "prod-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="prod-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "prod-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/prod-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="prod-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
          after-script:
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${ENVIRONMENT}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi
    RollBack-4-Staging:
      - variables:
        - name: VERSION
      - step:
          name: RollBack Staging
          deployment: Staging
          script:
            - apt-get update && apt-get install -y awscli jq
            - S3_BUCKET_BACKUP="s3://deploy-bckups/staging/extended-pricing-widget"
            - S3_BUCKET="s3://extended-widget-ui-staging-one"
            - aws s3 cp $S3_BUCKET_BACKUP/$VERSION/ $S3_BUCKET --recursive
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_STAGING" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_STAGING" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
    Deployment-4-Staging:
      - step:
          name: Build, Upload, Invalidate and Tag
          deployment: Staging
          caches:
            - node
          size: 2x
          script:
            - export NODE_OPTIONS="--max-old-space-size=6144"
            - apt-get update && apt-get install -y awscli jq
            - aws --version # Verify AWS CLI is available
             # start deplyoment notification
            - MY_ENV="$ENVIRONMENT"
            - echo $MY_ENV
            - echo "export MY_ENV=$MY_ENV" >> set_env.sh
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - >
              curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${MY_ENV}\",\"status\":\"${START}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
            # Set necessary variables for Current Build   
            - S3_BUCKET="s3://extended-widget-ui-staging-one"
            - aws s3 cp $S3_BUCKET/buildversion1.ver .
            - read=$(cat buildversion1.ver)
            - echo $read | jq -r '.BuildVersion' > buildversion.txt
            - version=$(cat buildversion.txt)
            - echo "$version"
            - IFS='-' read -ra parts <<< "$version"
            - date="${parts[0]}"
            - buildnumber="${parts[1]}"
            - echo "$date"
            - echo "$buildnumber"
            - TODAY_DATE=$(date +'%Y%m%d')
            - echo "$TODAY_DATE"
            - if [[ "$date" == "$TODAY_DATE" ]]; then
                build_number=$((buildnumber + 1));
              else
                build_number=1;
              fi
            - var2="$TODAY_DATE-$build_number"
            - echo "$var2"
            # set renderer version
            - TODAY_DATE_V=$(date +'%d-%b-%y')
            - echo "$TODAY_DATE_V"
            - renderer_version="staging-$TODAY_DATE_V-$build_number"
            - echo "$renderer_version"
            - env_fileName=".env.staging"
            - node ./scripts/update-renderer "$renderer_version" "$env_fileName"
            # start installation and build
            - npm install
            - npm run build:web:staging
            # Set necessary variables for Backup
            - S3_BUCKET="s3://extended-widget-ui-staging-one"
            - S3_BACKUP_BUCKET="s3://deploy-bckups/staging/extended-pricing-widget"            
            - LOCAL_PATH="./build/"
            - EXCLUDE_FILES='--exclude=robots.txt'
            # Deploy backup to S3 using AWS CLI
            - aws s3 cp --recursive $S3_BUCKET $S3_BACKUP_BUCKET/$version/ $EXCLUDE_FILES
            # Deploy to S3 using AWS CLI
            - echo "{\"BuildVersion\":\"$TODAY_DATE-$build_number\"}" > buildversion1.ver
            - aws s3 rm $S3_BUCKET/ $EXCLUDE_FILES --recursive
            - aws s3 sync $LOCAL_PATH $S3_BUCKET/ --delete
            - aws s3 cp buildversion1.ver $S3_BUCKET
            # Trigger CloudFront invalidation and capture the response
            - INVALIDATION_RESPONSE=$(aws cloudfront create-invalidation --distribution-id "$DISTRIBUTION_ID_STAGING" --paths "/*")
            # Extract the Invalidation ID from the response and store it in an environment variable
            - INVALIDATION_ID=$(echo "$INVALIDATION_RESPONSE" | jq -r '.Invalidation.Id')
            - echo "${INVALIDATION_ID}"
            - |
              while :
              do
                STATUS=$(aws cloudfront get-invalidation --distribution-id "$DISTRIBUTION_ID_STAGING" --id "$INVALIDATION_ID" --query 'Invalidation.Status' --output text)
                if [[ "$STATUS" == "Completed" ]]; then
                  echo "CloudFront invalidation is complete!"
                  break
                elif [[ "$STATUS" == "InProgress" ]]; then
                  echo "CloudFront invalidation is in progress. Waiting..."
                  sleep 10
                else
                  echo "CloudFront invalidation failed or unknown status."
                  break
                fi
              done
            - echo "DONE"
            # Notify external service about new version
            - >
              curl --location 'https://bryzoswidget.com/widget-service/external-affairs/render/version' --header 'Content-Type: application/json' --data "{\"data\": \"$renderer_version\"}"
            # Create tag
            - TAG_EXISTS=$(git tag -l "staging-$(date +'%d-%b-%y')-*" | wc -l)
            - echo "$TAG_EXISTS";
            - if [ "$TAG_EXISTS" -eq 0 ]; then
               NEW_TAG="staging-$(date +'%d-%b-%y')-1";
              else
               LAST_TAG=$(git tag -l "staging-$(date +'%d-%b-%y')-*" | sort -r | head -n 1);
               LAST_NUMBER=$(echo "$LAST_TAG" | sed 's/staging-.*-\([0-9]*\)/\1/');
               NEW_NUMBER=$((LAST_NUMBER + 1));
               NEW_TAG="staging-$(date +'%d-%b-%y')-$NEW_NUMBER";
              fi
            - git tag "$NEW_TAG"
            - git push origin "$NEW_TAG"
            - echo "$NEW_TAG"
            - echo "tag created successfully"
          after-script:
            - cat set_env.sh 
            - source set_env.sh
            - echo $MY_ENV
            - apt-get update 
            - apt-get -y install jq
            - export user_name=$(curl -X GET -g "https://api.bitbucket.org/2.0/users/${BITBUCKET_STEP_TRIGGERER_UUID}" | jq --raw-output '.display_name')
            - echo "$user_name"
            - export PIPELINE_URL="https://bitbucket.org/${BITBUCKET_REPO_OWNER}/${BITBUCKET_REPO_SLUG}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}"
            - echo "$PIPELINE_URL"
            - |
              echo "BITBUCKET_EXIT_CODE: $BITBUCKET_EXIT_CODE"

              if [[ $BITBUCKET_EXIT_CODE -eq 0 ]]; then
                echo "Build successful"
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${MY_ENV}\",\"status\":\"${SUCCESS}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK   
              else
                echo "Build failed"
                # Add your error notification logic here
                curl -X POST -H "Content-Type: application/json" --data "{\"repo\":\"${BITBUCKET_REPO_SLUG}\",\"who\":\"${user_name}\",\"env\":\"${MY_ENV}\",\"status\":\"${ERROR}\",\"url\":\"${PIPELINE_URL}\",\"type\":\"${TEAM_UI}\"}"  $BUILD_NOTIFICATION_LINK
              fi

           
            
                
          
 