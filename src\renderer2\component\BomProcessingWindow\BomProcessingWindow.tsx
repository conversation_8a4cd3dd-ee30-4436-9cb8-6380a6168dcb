import React, { useEffect, useRef, useState } from 'react'
import clsx from 'clsx'
import styles from './BomProcessingWindow.module.scss'
import { axios, useCreatePoStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { clearLocal, navigatePage, setLocal } from 'src/renderer2/helper';
import { commomKeys, localStorageKeys, routes, snackbarSeverityType } from 'src/renderer2/common';
import useGetGameScore from 'src/renderer2/hooks/useGetGameScore';
import ProgressBar from '../ProgressBar/ProgressBar';
import { useRightWindowStore } from 'src/renderer2/pages/RightWindow/RightWindowStore';
import useSnackbarStore from '../Snackbar/snackbarStore';
import { v4 as uuidv4 } from 'uuid';
import usePostBomUploadS3Url from 'src/renderer2/hooks/usePostBomUploadS3Url';

interface BomProcessingWindowProps {
    hideScore?: boolean;
}

// Define the type for bomProcessingWindowProps
interface BomProcessingWindowState {
    uploadingPercent?: number;
    readingPercent?: number;
    thinkingPercent?: number;
    generatingPercent?: number;
    currentScore?: number;
    isProcessing?: boolean;
    bomUploadId?: string;
    isCompleted?: boolean;
    gameScoreData?: any;
    [key: string]: any; // Allow for other properties
}

const BomProcessingWindow: React.FC<BomProcessingWindowProps> = (
    { 
        hideScore = false,
    }
) => {
    const {userData} = useGlobalStore();
    const {bomGameScore} = useCreatePoStore();
    const { bomProcessingError, setBomProcessingError, bomProductMappingSocketData, bomProgressSocketData, setBomProgressSocketData, setIsCreatePOModule, setBomProductMappingSocketData, uploadBomInitialData } = useCreatePoStore();
    const { deviceId } = useGlobalStore();
    const { bomProcessingWindowProps, setBomProcessingWindowProps, isCancelUpload, setIsCancelUpload, setLoadComponent } = useRightWindowStore();
    const [isCompleted, setIsCompleted] = useState(false);
    //const { data: gameScoreData, isLoading: isGameScoreLoading } = useGetGameScore();
    const [userTopScore, setUserTopScore] = useState(0);
    const [userRank, setUserRank] = useState('');
    const [allBryzosTopScore, setAllBryzosTopScore] = useState('');
    const [ordinalSuffix, setOrdinalSuffix] = useState('');
    const [gameScoreData, setGameScoreData] = useState<any>(null);
    const { currentScore, isProcessing, bomUploadId, fileToUpload } = bomProcessingWindowProps;
    const [uploadingPercent, setUploadingPercent] = useState<number>(0);
    const [readingPercent, setReadingPercent] = useState(0);
    const [thinkingPercent, setThinkingPercent] = useState(0);
    const [generatingPercent, setGeneratingPercent] = useState(0);

    // Replace the displayPercentages object with four separate state variables
    const [uploadingDisplayPercentage, setUploadingDisplayPercentage] = useState(0);
    const [readingDisplayPercentage, setReadingDisplayPercentage] = useState(0);
    const [thinkingDisplayPercentage, setThinkingDisplayPercentage] = useState(0);
    const [generatingDisplayPercentage, setGeneratingDisplayPercentage] = useState(0);

    //Parms for the timer. 
    const isReadingTimerRunning = useRef(false);
    const isThinkingTimerRunning = useRef(false);
    const isGeneratingTimerRunning = useRef(false);
    // Reference to abort controller for cancelling uploads
    const uploadAbortController = useRef<AbortController | null>(null);

    const divisor = 2;//This is used to divide the time interval (appreox 1 second). We will need to divide the incremetns by the same value as they represent the increment per second
    const readingIncrement = 1.5/divisor;//This is the increment per second for the reading progress bar (before divisor)
    const thinkingIncrement = .25/divisor;//This is the increment per second for the thinking progress bar (before divisor)
    const generatingIncrement = 2/divisor;//This is the increment per second for the generating progress bar (before divisor)
    const timerInterval = 950/divisor;//This is the time interval for the timer (before divisor)
    
    const {
        mutate: saveBomUploadS3Url,
        data: saveBomUploadS3UrlData,
        isLoading: isSaveBomUploadS3UrlLoading,
    } = usePostBomUploadS3Url();

    useEffect(()=>{
       const timer = setInterval(()=>{
        if(isReadingTimerRunning.current){
            setReadingPercent(prev=>Math.min(prev+readingIncrement,100));
        }else if(isThinkingTimerRunning.current){
            setThinkingPercent(prev=>Math.min(prev+thinkingIncrement,100));
        }else if(isGeneratingTimerRunning.current){
            setGeneratingPercent(prev=>Math.min(prev+generatingIncrement,99));
        }
        if(uploadingDisplayPercentage === 100 && readingDisplayPercentage === 100 && thinkingDisplayPercentage === 100 && generatingDisplayPercentage === 100){
            //all done time to wrap up
            clearInterval(timer);
        }
       }, timerInterval);

        return ()=>timer?clearInterval(timer):null;
    },[]);

    const updateDiplayFlags = () => {
        // Start reading phase when uploading is complete
        if(uploadingDisplayPercentage === 100 && readingDisplayPercentage < 100 && !isReadingTimerRunning.current){
            isReadingTimerRunning.current = true;
            isThinkingTimerRunning.current = false;
            isGeneratingTimerRunning.current = false;
        }
        // Start thinking phase when reading is complete
        else if(readingDisplayPercentage === 100 && thinkingDisplayPercentage < 100 && !isThinkingTimerRunning.current){
            isThinkingTimerRunning.current = true;
            isReadingTimerRunning.current = false;
            isGeneratingTimerRunning.current = false;
        }
        // Start generating phase when thinking is complete
        else if(thinkingDisplayPercentage === 100 && generatingDisplayPercentage < 100 && !isGeneratingTimerRunning.current){
            isGeneratingTimerRunning.current = true;
            isThinkingTimerRunning.current = false;
            isReadingTimerRunning.current = false;
        }
        // All progress complete
        else if (generatingDisplayPercentage === 100) {
            isGeneratingTimerRunning.current = false;
            isThinkingTimerRunning.current = false;
            isReadingTimerRunning.current = false;
        }
    }

    // This effect will run whenever the display percentages change
    useEffect(()=>{
        updateDiplayFlags();
    },[uploadingDisplayPercentage, readingDisplayPercentage, thinkingDisplayPercentage, generatingDisplayPercentage]);

    useEffect(()=>{
        if(bomProcessingWindowProps?.gameScoreData){
            setGameScoreData(bomProcessingWindowProps?.gameScoreData);
        }
        if(bomProcessingWindowProps?.isCompleted === true){
            setIsCompleted(true);
            setGeneratingPercent(100);
        }
        console.log("bomprocessingwindow bomProcessingWindowProps", bomProcessingWindowProps?.stopProgress);
        if(bomProcessingWindowProps?.stopProgress === true){
            isReadingTimerRunning.current = false;
            isThinkingTimerRunning.current = false;
            isGeneratingTimerRunning.current = false;
            setUploadingPercent(0);
            setReadingPercent(0);
            setThinkingPercent(0);
            setGeneratingPercent(0);
        }
    },[bomProcessingWindowProps]);

    useEffect(()=>{
        if(bomGameScore){
            setUserTopScore(bomGameScore?.userRank?.score || "");
            setUserRank(bomGameScore?.userRank?.rank || "");
            setOrdinalSuffix(getOrdinalSuffix(bomGameScore?.userRank?.rank || ''));
            setAllBryzosTopScore(bomGameScore?.overall_highest_score || "0");
        }
    },[bomGameScore]);

    // Handle BOM progress socket data
    useEffect(() => {
        if (bomProgressSocketData) {
            // Check if this socket message is for our current BOM upload
            if(userData.data.email_id !== bomProgressSocketData.email_id || deviceId !== bomProgressSocketData.device_id || bomUploadId !== bomProgressSocketData.bom_upload_id) {
                console.log('Socket message is for a different user or device or BOM upload, ignoring');
                return;
            }

            setLocal(localStorageKeys.bomData, {bomProgressSocketData, bomProcessingWindowProps});
            
            const percentage = bomProgressSocketData.progressPercentage;

            switch (bomProgressSocketData.event) {
                case 'bomGeneratingProgressEvent':
                    if(bomProcessingWindowProps.isCompleted){
                        setGeneratingPercent(100);
                    }else{
                        // Cap generating progress at 99%
                        setGeneratingPercent(Math.min(percentage, 99));
                    }
                    break;
                case 'bomThinkingProgressEvent':
                    setThinkingPercent(prev=>Math.max(prev,percentage));
                    break;
                case 'bomReadingProgressEvent':
                    setReadingPercent(prev=>Math.max(prev,percentage));
                    break;
                default:
                    console.log('Unknown event type:', bomProgressSocketData.event);
            }
        }
    }, [bomProgressSocketData, deviceId, bomUploadId, setBomProcessingWindowProps]);

    useEffect(() => {
        if(currentScore > userTopScore) {
            setUserTopScore(currentScore);
        }
    }, [currentScore]);
    
    // Reset internal state when all percentages are 0 (new upload started)
    useEffect(() => {
        const allPercentagesZero = 
            uploadingPercent === 0 && 
            readingPercent === 0 && 
            thinkingPercent === 0 && 
            generatingPercent === 0;
            
        if (allPercentagesZero) {
            console.log('BomProcessingWindow: Resetting all display percentages to 0');
            setUploadingDisplayPercentage(0);
            setReadingDisplayPercentage(0);
            setThinkingDisplayPercentage(0);
            setGeneratingDisplayPercentage(0);
        }
    }, [uploadingPercent, readingPercent, thinkingPercent, generatingPercent]);
    
    // Update display percentages based on incoming props and progression rules
    useEffect(() => {

        setLocal(localStorageKeys.bomData, {bomProgressSocketData, bomProcessingWindowProps});
        
        // Progressive calculations
        let newUploading = Math.max(uploadingDisplayPercentage, uploadingPercent);
        
        // If reading has started, ensure uploading is complete
        if (readingPercent > 0) {
            newUploading = 100;
        }
        
        // Never decrease values and ensure progression
        let newReading = Math.max(readingDisplayPercentage, readingPercent);
        if (thinkingPercent > 0) {
            newReading = 100;
            newUploading = 100;
        }
        
        let newThinking = Math.max(thinkingDisplayPercentage, thinkingPercent);
        if (generatingPercent > 0) {
            newThinking = 100;
            newReading = 100;
            newUploading = 100;
        }
        
        let newGenerating = Math.max(generatingDisplayPercentage, generatingPercent);

        // Update each state variable separately
        setUploadingDisplayPercentage(newUploading);
        setReadingDisplayPercentage(newReading);
        setThinkingDisplayPercentage(newThinking);
        setGeneratingDisplayPercentage(newGenerating);
        
    }, [uploadingPercent, readingPercent, thinkingPercent, generatingPercent]);
    const {showToastSnackbar, setSnackbarOpen} = useSnackbarStore();
    const handleSnackbarClose = () => {
        setSnackbarOpen(false);
    }
    
    useEffect(()=>{
        if( 
            bomProcessingError?.bom_upload_id === bomUploadId && 
            bomProcessingError?.email_id === userData.data.email_id && 
            bomProcessingError?.device_id === deviceId){
            setBomProcessingError(null);
            clearLocal(localStorageKeys.bomData);
            setUploadingDisplayPercentage(0);
            setReadingDisplayPercentage(0);
            setThinkingDisplayPercentage(0);
            setGeneratingDisplayPercentage(0);
            // showToastSnackbar("Something went wrong, please try again.", snackbarSeverityType.alert, [{name:commomKeys.tryAgain, handler: handleSnackbarClose}], null, null);
            setBomProcessingWindowProps({
                isProcessing: false,
                isCompleted: false,
                stopProgress: true,
                generatingPercent: 0,
                readingPercent: 0,
                thinkingPercent: 0,
                uploadingPercent: 0
            });
        }
    },[bomProcessingError]);
    
    // Determine if processing is complete based on displayed percentages
    const isProcessingCompleteDisplay = 
        uploadingDisplayPercentage === 100 && 
        readingDisplayPercentage === 100 && 
        thinkingDisplayPercentage === 100 && 
        generatingDisplayPercentage === 100;
    
    const handleNext = () => {
        setIsCreatePOModule(false);
        setBomProcessingWindowProps({
            isProcessing: false,
            isCompleted: false,
            generatingPercent: 0,
            readingPercent: 0,
            thinkingPercent: 0,
            uploadingPercent: 0
        });
        setBomProgressSocketData(null);
        //setBomProductMappingSocketData(null);
        clearLocal(localStorageKeys.bomData);
        navigatePage(location.pathname, { path: routes.bomUploadReview })
    }

    const handleCancel = () => {
        // Cancel any in-progress uploads
        if (uploadAbortController.current) {
            uploadAbortController.current.abort();
            uploadAbortController.current = null;
        }
        // Stop all progress timers
        isReadingTimerRunning.current = false;
        isThinkingTimerRunning.current = false;
        isGeneratingTimerRunning.current = false;
        
        // Set cancel flag to trigger parent component cleanup
        setIsCancelUpload(true);
        
        // Reset local state
        setUploadingPercent(0);
        setReadingPercent(0);
        setThinkingPercent(0);
        setGeneratingPercent(0);
        setUploadingDisplayPercentage(0);
        setReadingDisplayPercentage(0);
        setThinkingDisplayPercentage(0);
        setGeneratingDisplayPercentage(0);
        
        // Clear any local storage related to the BOM upload
        clearLocal(localStorageKeys.bomData);
        setBomProcessingWindowProps({
            isProcessing: false,
            isCompleted: false,
            generatingPercent: 0,
            readingPercent: 0,
            thinkingPercent: 0,
            uploadingPercent: 0
        });
        setBomProgressSocketData(null);
        setLoadComponent(null);
    }

    // Function to get the correct ordinal suffix for rank
    const getOrdinalSuffix = (rank: number) => {
        if(!rank) return "";
        if (rank === 1) return 'st';
        if (rank === 2) return 'nd';
        if (rank === 3) return 'rd';
        return 'th';
    }
    useEffect(() => {
        if(gameScoreData){
            setUserTopScore(gameScoreData?.user_highest_score?.score || "");
            setUserRank(gameScoreData?.user_rank || "");
            setOrdinalSuffix(getOrdinalSuffix(gameScoreData?.user_rank || ''));
            setAllBryzosTopScore(gameScoreData?.overall_highest_score?.score || "0");
        }
    }, [gameScoreData]);

    useEffect(() => {
        if(bomProductMappingSocketData){
            setBomProcessingWindowProps({isCompleted:true,generatingPercent:100});
        }
    }, [bomProductMappingSocketData]);

    useEffect(() => {
        if (saveBomUploadS3UrlData && saveBomUploadS3UrlData?.bom_upload_id) {
            setBomProcessingWindowProps({bomUploadId: saveBomUploadS3UrlData.bom_upload_id});  
        }
    }, [saveBomUploadS3UrlData]);
    
    // BOM File Upload Code - Handles file upload to S3
    useEffect(() => {
        if(fileToUpload){
            handleUpload(fileToUpload);
        }
    }, [fileToUpload]);

    const handleUpload = async (file: File) => {
        const fileName = file.name.substring(0, file.name.lastIndexOf(".")) || file.name;
        try {
            // Attempt to upload the file
            await uploadFileWithProgress(file, fileName);
        } catch (error) {
            console.log('error', error);
            setBomProcessingWindowProps({
                ...bomProcessingWindowProps,
                bomUploadError: true
            })
        }  
    }
    
    // Custom upload function that tracks progress
    const uploadFileWithProgress = async (file: File, fileName: string) => {
        try {
            // Create abort controller for this upload
            uploadAbortController.current = new AbortController();
            // Get API URL and params from environment or config
            const API_SERVICE = import.meta.env.VITE_API_SERVICE;
            const S3_BUCKET_NAME = import.meta.env.VITE_S3_UPLOAD_BOM_BUCKET_NAME;
            const ENVIRONMENT = import.meta.env.VITE_ENVIRONMENT;
            const ext = file.name.split('.').pop();
            const objectKey = ENVIRONMENT + '/bom/' + fileName + '-' + uuidv4() + '.' + ext;
            const signedUrlPayload = {
                data: {
                    "bucket_name": S3_BUCKET_NAME,
                    "object_key": objectKey,
                    "expire_time": 3000
                }
            }

            // Check if upload was cancelled before proceeding
            if (isCancelUpload) {
                setIsCancelUpload(false);
                return;
            }

            // Get signed URL for upload
            const signedUrlResponse = await axios.post(`${API_SERVICE}/user/get_signed_url`, signedUrlPayload);
            const signedUrl = signedUrlResponse.data.data;
            
            // Check again if upload was cancelled before proceeding
            if (isCancelUpload) {
                setIsCancelUpload(false);
                return;
            }
            
            // Upload the file to S3 with progress tracking
            const uploadResponse = await axios.put(signedUrl, file, {
                headers: {
                    'Content-Type': file.type,
                },
                onUploadProgress: (progressEvent) => {
                    if (progressEvent.total) {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        setUploadingPercent(percentCompleted);
                    }
                },
                signal: uploadAbortController.current?.signal
            });
            
            // Get the S3 URL
            const s3Url = signedUrl.split('?')[0];
            
            // Check if cancelled again before saving the URL
            if (isCancelUpload) {
                setIsCancelUpload(false);
                return;
            }
            
            // Save the uploaded file URL
            const payload = {
                data: {
                    actual_file_name: file.name,
                    s3_url: s3Url,
                    device_id:deviceId,
                    delivery_date:uploadBomInitialData?.delivery_date,
                    shipping_details:{
                        line1:uploadBomInitialData?.shipping_details?.line1,
                        line2:uploadBomInitialData?.shipping_details?.line2 || "",
                        city:uploadBomInitialData?.shipping_details?.city,
                        state_id:uploadBomInitialData?.shipping_details?.state_id,
                        zip:uploadBomInitialData?.shipping_details?.zip
                    },
                    bom_type:uploadBomInitialData?.order_type,
                    bom_name:uploadBomInitialData?.internal_po_number
                }
            };
            
            saveBomUploadS3Url(payload);
            return s3Url;
            
        } catch (error) {
            if (axios.isCancel(error)) {
                console.log('Upload canceled');
            } else {
                console.error('Upload error:', error);
                throw error;
            }
        }
    };

    return (
        <div className={clsx(hideScore ? styles.createPOPageContainer : styles.container)}>
            <div className={styles.fadeIn}>
                <>
                    {/* BOM Processing */}
                    <div className={styles.slideUp} style={{ animationDelay: '100ms' }}>
                        {/* Leaderboard Section */}
                        {!hideScore && <div className={styles.leaderboard}>
                            <div className={styles.leaderboardTitle}>
                                LEADERBOARD
                            </div>
                            <div className={styles.scoreContainer}>
                                <div className={styles.scoreRow1}>
                                    <div className={styles.scoreLabel1}>
                                        <span className={styles.place}>{userRank}<sup>{ordinalSuffix}</sup></span>
                                        <span className={styles.placeLabel}>PLACE</span>
                                    </div>
                                    <div className={styles.scoreValue}>
                                        <div className={styles.scoreLabel}>ALL-CSW  <br /> TOP SCORE</div>
                                        <div className={styles.score}>{allBryzosTopScore}</div>
                                    </div>
                                </div>
                                <div className={clsx(styles.scoreRow,'scoreRow2')}>
                                    <div className={styles.scoreLabel}>YOUR TOP SCORE</div>
                                    <div className={styles.score}>{userTopScore}</div>
                                </div>
                                <div className={clsx(styles.scoreRow,styles.currentGameScore)}>
                                    <div className={clsx(styles.scoreLabel,styles.currentGameLbl)}>CURRENT GAME</div>
                                    <div className={styles.score}><span>{currentScore}</span></div>
                                </div>
                            </div>
                        </div>}

                        {/* BOM Processing Section */}
                        <div className={styles.processingSection}>
                            <div className={styles.processingSectionTitle}>
                                BOM PROCESSING
                            </div>
                            <div className={styles.progressContainer}>
                                <ProgressBar 
                                    label="UPLOADING" 
                                    percent={uploadingDisplayPercentage} 
                                />
                                <ProgressBar 
                                    label="READING" 
                                    percent={readingDisplayPercentage} 
                                />
                                <ProgressBar 
                                    label="THINKING" 
                                    percent={thinkingDisplayPercentage} 
                                />
                                <ProgressBar 
                                    label="GENERATING" 
                                    percent={generatingDisplayPercentage} 
                                />
                            </div>
                        </div>

                        {/* Next Button */}
                        <button
                            id="next-btn"
                            className={`${styles.orderButton} ${styles.slideUp}`}
                            style={{ animationDelay: '500ms' }}
                            onClick={handleNext}
                            disabled={!(isCompleted || isProcessingCompleteDisplay)}
                        >
                            NEXT
                        </button>
                        {(uploadingDisplayPercentage !== 100) &&
                            <button
                                id="cancel-btn"
                                className={`${styles.cancelUploadButton}`}
                                onClick={handleCancel}
                            >
                                CANCEL UPLOAD
                            </button>
                        }
                    </div>
                </>
            </div>
        </div>
    );
}

export default BomProcessingWindow