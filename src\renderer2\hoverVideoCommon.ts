export const hoverVideoConfig = {

    'invite-user': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_App_Header_Items/Invite_Team_Button.mp4",
        title: '',//"Invite Users",
        description: "Invite your team to join CSW here"
    },
    'global-search': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_App_Header_Items/Master_Search_Bar.mp4",
        title: '',//"Invite Users",
        description: "Use this search bar to search your entire CSW account"
    },
    'notification': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_App_Header_Items/Notifications_Button.mp4",
        title: '',//"Invite Users",
        description: "View all your CSW notifications here"
    },
    'settings': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_App_Header_Items/Settings_Icon.mp4",
        title: '',//"Invite Users",
        description: "To access user settings, click the gear icon"
    },
    'subscribe': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_App_Header_Items/Subscription_Buton.mp4",
        title: '',//"Invite Users",
        description: "To Subscribe to CSW and access premium features, click the Subscribe button or access through your Settings!"
    },
    'deleted-items': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_App_Header_Items/The_Trash_Icon.mp4",
        title: '',//"Invite Users",
        description: "Access all your previously deleted items by clicking the Trash icon"
    },
    'om-duplicate': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Order_Management_Screen/Duplicate_Button_in_OM.mp4",
        title: '',//"Invite Users",
        description: "Click 'Duplicate' to create a reorder of that PO"
    },
    'om-edit': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Order_Management_Screen/Edit_Order_Button_in_OM.mp4",
        title: '',//"Invite Users",
        description: "Make changes like updating the Job/PO #, delivery info, quantities, or lines. Edits must be made here (not in Vendor Chat) and only before items have shipped."
    },
    'om-left-panel': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Order_Management_Screen/Left_side_panel_in_OM.mp4",
        title: '',//"Invite Users",
        description: "Use the left side panel to view all your previous orders placed on CSW."
    },
    'om-icon': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Order_Management_Screen/O_Icon_for_Order_Management.mp4",
        title: '',//"Invite Users",
        description: "Use the left side panel to view all your previous orders placed on CSW."
    },
    'om-po-view': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Order_Management_Screen/Previous_Order_Details_in_OM.mp4",
        title: '',//"Invite Users",
        description: "Select a previous order to view order details, line items, pricing, and supplier."
    },
    'om-chat': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Order_Management_Screen/Vendor_Chat_Demo.mp4",
        title: '',//"Invite Users",
        description: "Message the supplier directly to get shipping updates or discuss potential order changes. Note: all order changes must be submitted through the Edit Order function."
    },
    'price-search-create-new': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Create_New_in_Price_Search.mp4",
        title: '',//"Invite Users",
        description: "Select the Create New button to start a new price search session."
    },
    'search-zip': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Destination_Zip_Code_Video.mp4",
        title: "",//"Regional Pricing",
        description: "Enter your destination ZIP code to reflect regional-based prices while searching."
    },
    'domestic-only-search': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Domestic_Required_Video.mp4",
        title: "",//"Marking Domestic Only on your PO",
        description: "Need domestic-only materials? Use the ‘Domestic Required’ filter for more accurate results while price searching."
    },
    'order-size-on-search': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Order_Weight_Drop_Down.mp4",
        title: "",//"Order Weight Feature",
        description: "Select your estimated order weight for more accurate pricing."
    },
    'pricing-feedback-on-search': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Pricing_Feedback_Video.mp4",
        title: "",//"Pricing Feedback",
        description: "Click any price to submit quick pricing feedback—just enter amount, unit, quantity, and ZIP."
    },
    'pricing-unit-on-search': {
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Pricing_Unit_Price_Search_Video.mp4",
        title:  "",//"Pricing Units",
        description: "Select how you want to view pricing: $/FT, $/LB, or $/CWT."
    },

    'price-search-icon':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Lightning_Bolt_icon_Left_side_menu.mp4",
        title:  "",//"Pricing Units",
        description:  "Click the Lightning Bolt icon to access the Price Search screen" 
    },
    'price-search-line':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Line_Actions_for_Price_Search.mp4",
        title:  "",//"Pricing Units",
        description:  "Click a line to add to PO, add to quote, share pricing, export pricing, or delete lines"
    },
    'price-search':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/New_How_to_Do_a_Price_Search_Demo.mp4",
        title: "",//"How To Do A Price Search",
        description: "To do a Price Search, start by selecting the item shape, then enter specs or grade and choose from the dropdown."
    },
    'search-left-panel':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Price_Search_Saved_Search_List.mp4",
        title: "",//"How To Do A Price Search",
        description: "View all your auto-saved previous price search sessions on the left side panel."
    },
    //purchase below
    'purchase-upload-bom':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Upload_BOM_Video_for_purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "Upload your material list—CSW extracts, matches, and prices it into a ready-to-go PO in seconds."
    },
	'purchase-pricing-bracket':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Pricing_Bracket_Video_for_purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "Add more weight, unlock better pricing—your bracket updates automatically"
    },
	'purchase-part-number':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Part_Field_Video_for_purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "Add your part number to any CSW item—it’s saved to your account, so you can search and reorder using your own terms next time."
    },
	'purchase-left-panel':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Left_Side_Panel_Auto_Save_POs.mp4",
        title: "",//"How To Do A Price Search",
        description: "Use the left side panel to access all your auto-saved POs."
    },
	'purchase-domestic-only':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Domestic_Only_in_PO_Video.mp4",
        title: "",//"How To Do A Price Search",
        description: "Need Domestic Only on a specific line? Click the line number to the left."
    },
	'purchase-update-pricing':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Update_Pricing_Button_in_Purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "When in an old PO, click Update Pricing to reflect the most accurate CSW pricing to continue with your order." 
    },
	'purchase-header':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/PO_Header_Info_Video.mp4",
        title: "",//"How To Do A Price Search",
        description: "Start your PO by filling out key details—job info, delivery date, type, and address."
    },	
	'purchase-icon':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/P_Icon_for_Purchasing_Screen.mp4",
        title: "",//"How To Do A Price Search",
        description:"Click the [P] icon on the left side navigation to access the Purchasing screen"
    },
	'purchase-create-po':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Entering_in_a_Line_in_a_PO_Video.mp4	",
        title: "",//"How To Do A Price Search",
        description: "Enter PO lines just like a price search—type shape and specs, select an item, then add your quantity."
    },	
	'purchase-create-new':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Price_Search_Screen/Price_Search_Saved_Search_List.mp4",
        title: "",//"How To Do A Price Search",
        description: "To begin a new PO, click Create New" 
    },

    //quoting below
    'quoting-part-number':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Part_Field_Video_for_purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "Add your part number to any CSW item—it’s saved to your account, so you can search and reorder using your own terms next time."
    },
	'quoting-domestic-only':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Domestic_Only_in_PO_Video.mp4",
        title: "",//"How To Do A Price Search",
        description: "Need Domestic Only on a specific line? Click the line number to the left."
    },
	'quoting-update-pricing':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Update_Pricing_Button_in_Purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "When in an old PO, click Update Pricing to reflect the most accurate CSW pricing to continue with your order." 
    },
    'quoting-upload-bom':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Upload_BOM_Video_for_purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "Upload your material list—CSW extracts, matches, and prices it into a ready-to-go PO in seconds."
    },
	'quoting-pricing-bracket':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Purchasing_Screen/Pricing_Bracket_Video_for_purchasing.mp4",
        title: "",//"How To Do A Price Search",
        description: "Add more weight, unlock better pricing—your bracket updates automatically"
    },
    'quoting-header':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Quoting_Screen/Quote_Header_Info.mp4",
        title: "",//"How To Do A Price Search",
        description:  "To begin a quote, fill in your header information first." 
    },
	'quoting-icon':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Quoting_Screen/Q_Icon_for_Quoting_Screen.mp4",
        title: "",//"How To Do A Price Search",
        description:  "Click the [Q] icon to access the Quoting screen"
    },
	'quoting-left-panel':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Quoting_Screen/Left_Side_Panel_for_Quotes.mp4",
        title: "",//"How To Do A Price Search",
        description: "Use the left side panel to access all your previously saved quotes."
    },
	'quoting-create-po':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Quoting_Screen/Entering_a_Line_in_a_Quoting.mp4",
        title: "",//"How To Do A Price Search",
        description:  "To enter lines in your quote, start with the shape and specs in the description box, then select from the dropdown."
    },
	'quoting-create-new':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Quoting_Screen/Create_New_Button_for_Quoting.mp4",
        title: "",//"How To Do A Price Search",
        description:  "Select Create New to begin a new quote."
    },
	'quoting-convert':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Quoting_Screen/Convert_to_PO_in_Quoting_Screen.mp4",
        title: "",//"How To Do A Price Search",
        description: "Select the Convert to PO button to move on to the purchasing screen" 
    },

    //settings below
    'settings-user':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/The_user_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description:  "Enter personal information in the User tab, include name, email, phone number, etc." 
    },
	'settings-notification':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/The_Notifications_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description: "The Notifications section allows you to manage how you receive all your CSW notifications." 
    },
	'settings-company':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/The_company_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description:"Enter and edit company information here."
    },
	'settings-subscription':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/Subscription_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description:"Set up and manage your CSW subscription here."
    },
	'settings-shipping':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/Shipping_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description: "Set up your all your shipments here, including locations, receiving hours, and shipping documents"
    },
	'settings-resale-cert':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/Resale_Certs_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description: "Upload and view your Resale Certificates here" 
    },
	'settings-payment':{
        videoUrl: "https://ik.imagekit.io/bryzos/video-library/video-tool-tips/For_Settings_Screens/Payments_Tab_in_Settings.mp4",
        title: "",//"How To Do A Price Search",
        description:   "Set up your payment method(s) here: apply for Net 30 terms, choose Cash in Advance, or enter credit card info."
    }

    // 'bryzos-logo':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-8a7c36d2-e33f-447c-b9fc-9c6f0a831b48.mp4",
    //     title: "Return to Home",
    //     description: "Click the Bryzos 'B' anytime to return to the Home Screen."
    // },
    // 'pin':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-69035f9f-3961-4acf-ae5a-32073ee5bbe4.mp4",
    //     title: "Pin Your Widget",
    //     description: "Keep Bryzos on top of your screen—always visible, always accessible."
    // },
    // 'main-menu':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-422178a3-1ae5-4454-8946-f385a0d3d3f8.mp4",
    //     title: "Main Menu Navigation",
    //     description: "Navigate your Bryzos app easily—jump to Price Search, Create PO, Vendor Chat, Videos, and other tools from the main menu panel."
    // },
    // 'domestic-only-search':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-2cc604e4-14db-425d-a5ff-97489608dbaf.mp4",
    //     title: "Domestic Required",
    //     description: "Need domestic-only materials? Use the ‘Domestic Required’ filter for more accurate results while price searching."
    // },
    // 'entering-a-line-in-po':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-098307d9-50b5-46c8-ad76-d8c97a0caf0f.mp4",
    //     title: "Entering Your Lines",
    //     description: "Enter PO lines just like a price search—type shape and specs, select an item, then add your quantity."
    // },
    // 'price-search':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-f715a78f-fab9-471b-8aaf-7e825b0a84db.mp4",
    //     title: "How To Do A Price Search",
    //     description: "Start your price search by typing a shape, grade, or spec. Pick from the dropdown and see instant, real-time pricing."
    // },
    // 'line-action-on-search':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-c1ba05e7-99bf-4583-8c24-853b665fffd2.mp4",
    //     title: "Line Action Menu",
    //     description: "Click a line to view actions like Export, Save, Delete, and more."
    // },
    // 'order-size-on-search':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-2d0e7970-bd6c-448f-932f-84dbab634960.mp4",
    //     title: "Order Weight Feature",
    //     description: "Use the slider to set your order size—pricing updates with volume. Set a specific weight and save it as your default for future searches."
    // },
    // 'part-number-po':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-b235d161-9720-45c6-be65-169694cb0015.mp4",
    //     title: "Adding Your Part Number",
    //     description: "Add your part number to any Bryzos item—it’s saved to your account, so you can search and reorder using your own terms next time."
    // },
    // 'pricing-bracket-po':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-2191098d-3a0c-4d11-9765-0ca61bc386fc.mp4",
    //     title: "The Pricing Bracket",
    //     description: "Add more weight, unlock better pricing—your bracket updates automatically"
    // },
    // 'saved-bom':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-78f88080-d351-49d9-b498-2e4f48310d26.mp4",
    //     title: "Saved BOMs",
    //     description: "View, update, and convert your saved BOMs—all in one place"
    // },
    // 'upload-bom':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-b592fc4f-263a-41da-8e37-91fd25d9bf64.mp4",
    //     title: "Upload BOM",
    //     description: "Upload your material list—Bryzos extracts, matches, and prices it into a ready-to-order PO in seconds."
    // },
    // 'create-po-header':{
    //     videoUrl: "https://ik.imagekit.io/bryzos/video-library/staging/video-b328ad9a-7fb8-436c-8c8d-3dfeaa3c22af.mp4",
    //     title: "PO Header",
    //     description: "We need your key PO details - job number, destination, delivery date and the Bid or Buy status of this order."
    // },
}