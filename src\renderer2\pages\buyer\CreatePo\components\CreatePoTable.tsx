import clsx from 'clsx';
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import BomTile from '../../bomTile/BomTile';
import { useRightWindowStore } from '../../../RightWindow/RightWindowStore';
import { BNPLSTATUS, bomLineStatusCountObjDefault, creditLimitStatus, disputeOptions, localStorageKeys, routes } from 'src/renderer2/common';
import { useBomReviewStore } from '../../BomReview/BomReviewStore';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { bomReviewSchema } from 'src/renderer2/models/bomReview.model';
import { useFieldArray } from 'react-hook-form';
import { commomKeys, dateTimeFormat, formatCurrency, getFloatRemainder, getValUsingUnitKey, newPricingPrefix, noIdGeneric, orderIncrementPrefix, orderType, priceUnits, purchaseOrder, reactQueryKeys, uploadBomConst, useBuyerCheckOutNode, useBuyerSettingStore, useCreatePoStore, useGlobalStore, useOrderManagementStore, userRole, useSaveUserActivity } from '@bryzos/giss-ui-library';
import { useLocation, useNavigate } from 'react-router-dom';
import { calculateBuyerTotalOrderWeightForGear } from 'src/renderer2/utility/pdfUtils';
import useGetAvailableCreditLimit from 'src/renderer2/hooks/useGetAvailableCreditLimit';
import { useBomPdfExtractorStore } from '../../BomPdfExtractor/BomPdfExtractorStore';
import CreatePoTile from './CreatePoTile';
import useCreatePoPriceCalculation from 'src/renderer2/hooks/useCreatePoPriceCalculation';
import dayjs from 'dayjs';
import usePostDraftPo from 'src/renderer2/hooks/usePostDraftPo';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import { useQueryClient } from '@tanstack/react-query';
import { useDebouncedValue } from '@mantine/hooks';
import { checkAddressChanged, clearLocal, formatDraftFinalPayload, getLocal, handleDraftPoSave, setLocal, updateOrderManagementData } from 'src/renderer2/helper';
import customParseFormat from "dayjs/plugin/customParseFormat";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(customParseFormat);
dayjs.extend(utc);
dayjs.extend(timezone);
import { v4 as uuidv4 } from 'uuid';
import { MenuItem, Select } from '@mui/material';
import DisputeTile from 'src/renderer2/pages/OrderManagement/components/DisputeTile/DisputeTile';
import useGetOrderLines from 'src/renderer2/hooks/useGetOrderLines';
import NoBuyerSettingPopup from './NoBuyerSettingPopup/NoBuyerSettingPopup';
import { ReactComponent as FilterIcon } from '../../../../assets/New-images/New-Image-latest/filter-outlined.svg';
import { ReactComponent as DropdownIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';

interface FormErrorData {
    qty?: boolean;
    product?: boolean;
    qtyEmpty?: boolean;
}

interface FormErrors {
    [key: number]: FormErrorData;
}

let emptyCartItem = {
    descriptionObj: '',
    description: '',
    qty: '',
    qty_unit: '',
    price_unit: '',
    product_tag: '',
    domestic_material_only: false,
    qtyUnitM: [],
    priceUnitM: [],
    line_session_id: '',
}

const CreatePoTable = forwardRef<any, any>((
    {
        styles,
        createPoContainerRef,
        formInputGroupRef,
        hidePoLineScroll,
        setHidePoLineScroll,
        addPoLineTableRef,
        bomUploadResult,
        products,
        userPartData,
        sessionId,
        searchStringData,
        setSearchString,
        setDisableBidBuyNow,
        setOpenDeliveryToDialog,
        scrollToTop,
        currentBomData,
        scrollPoHeaderToBottom,
        setFocusJobPoInput,
        scrollerRef,
        setMaxScrollHeight,
        setCurrentBomData,
        isHeaderDetailsConfirmed,
        initializePoHeaderForm,
        isSavedBom,
        setOpenErrorDialog,
        setErrorMessage,
        setCameFromSavedBom,
        maxScrollHeight,
        isProgrammaticScroll,
        setIsProgrammaticScroll,
        cameFromSavedBom,
        pricingBrackets,
        isCreatePOModule,
        poHeaderFormWatch,
        setBomUploadResult,
        navigateWithConfirmation,
        saveUserActivity,
        getDeliveryDateData,
        setCreatePoSessionId,
        handleStoreUndoStack,
        setIsOrderLineChanges,
        isStateZipValChange,
        componentType,
        currentFocusedItem,
        setFinalDisputePayload,
        finalDisputePayload,
        removeFromAttentionItems,
        findItemAttention,      
        addToAttentionItems
    }, ref
) => {
    const navigate = useNavigate();
    const location = useLocation();
    const setShowLoader = useGlobalStore((state: any) => state.setShowLoader);
    const productMapping = useGlobalStore((state: any) => state.productMapping);
    const userData = useGlobalStore((state: any) => state.userData);
    const bomSummaryViewFilter = useCreatePoStore((state: any) => state.bomSummaryViewFilter);
    const setBomSummaryViewFilter = useCreatePoStore((state: any) => state.setBomSummaryViewFilter);
    const bomProductMappingDataFromSavedBom = useCreatePoStore((state: any) => state.bomProductMappingDataFromSavedBom);
    const createPoDataFromSavedBom = useCreatePoStore((state: any) => state.createPoDataFromSavedBom);
    const createPoData = useCreatePoStore((state: any) => state.createPoData);
    const selectedQuote = useCreatePoStore((state: any) => state.selectedQuote);
    const setSelectedQuote = useCreatePoStore((state: any) => state.setSelectedQuote);
    const setDraftOrderListFromSocket = useCreatePoStore((state: any) => state.setDraftOrderListFromSocket);
    const uploadBomInitialData = useCreatePoStore((state: any) => state.uploadBomInitialData);
    const setBomProductMappingSocketData = useCreatePoStore((state: any) => state.setBomProductMappingSocketData);
    const buyerSetting = useBuyerSettingStore((state: any) => state.buyerSetting);
    const setCreatePoData = useCreatePoStore((state: any) => state.setCreatePoData);
    const setUploadBomInitialData = useCreatePoStore((state: any) => state.setUploadBomInitialData);
    const setIsCreatePoDirty = useCreatePoStore((state: any) => state.setIsCreatePoDirty);
    const isCreatePoDirty = useCreatePoStore((state: any) => state.isCreatePoDirty);
    const setProps = useRightWindowStore((state: any) => state.setProps);
    const props = useRightWindowStore((state: any) => state.props);
    const showCommonDialog = useDialogStore((state: any) => state.showCommonDialog);
    const resetDialogStore = useDialogStore((state: any) => state.resetDialogStore);
    const setOpenLeftPanel = useLeftPanelStore((state: any) => state.setOpenLeftPanel);
    const setDisplayLeftPanel = useLeftPanelStore((state: any) => state.setDisplayLeftPanel);
    const leftPanelData = useLeftPanelStore((state: any) => state.leftPanelData);
    const setLeftPanelData = useLeftPanelStore((state: any) => state.setLeftPanelData);
    const orderInfoIsFilled = useCreatePoStore((state: any) => state.orderInfoIsFilled);
    const resetHeaderConfig = useGlobalStore((state: any) => state.resetHeaderConfig);
    const quoteList = useCreatePoStore((state: any) => state.quoteList);
    const setQuoteList = useCreatePoStore((state: any) => state.setQuoteList);
    const purchasingList = useCreatePoStore((state: any) => state.purchasingList);
    const setPurchasingList = useCreatePoStore((state: any) => state.setPurchasingList);
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const updatedDraftId = useCreatePoStore((state: any) => state.updatedDraftId);
    const draftOrderListFromSocket = useCreatePoStore((state: any) => state.draftOrderListFromSocket);
    const setBOMLineStatusCountObj = useRightWindowStore((state: any) => state.setBOMLineStatusCountObj);
    const setIsConvertingToPo = useCreatePoStore((state: any) => state.setIsConvertingToPo);
    const isEditingPo = useOrderManagementStore((state: any) => state.isEditingPo);
    const setIsEditingPo = useOrderManagementStore((state: any) => state.setIsEditingPo);
    const orderManagementSocketData = useOrderManagementStore((state: any) => state.orderManagementSocketData);
    const setOrderManagementSocketData = useOrderManagementStore((state: any) => state.setOrderManagementSocketData);
    const clickedCreateNewButton = useLeftPanelStore(state => state.clickedCreateNewButton);
    const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
    const isConvertingToPo = useCreatePoStore((state: any) => state.isConvertingToPo);
    const setBomData = useBomPdfExtractorStore(state => state.setBomData);
    const setSelectedCancelLines = useOrderManagementStore(state => state.setSelectedCancelLines);
    const selectedCancelLines = useOrderManagementStore(state => state.selectedCancelLines);
    const orderManagementData = useOrderManagementStore((state: any) => state.orderManagementData);
    const setOrderManagementData = useOrderManagementStore((state: any) => state.setOrderManagementData);
    const orderManageMentInitialData = useOrderManagementStore((state: any) => state.orderManageMentInitialData);
    const setShareEmailWindowProps = useRightWindowStore((state: any) => state.setShareEmailWindowProps);
    const setShareEmailType = useRightWindowStore((state: any) => state.setShareEmailType);
    const setOpenOrderFromList = useOrderManagementStore(state => state.setOpenOrderFromList);
    const setIsDisableLeftPanel = useLeftPanelStore(state => state.setIsDisableLeftPanel);
    const getAvailableCreditLimit = useGetAvailableCreditLimit();
    const logUserActivity = useSaveUserActivity();
    const [undoStackObject, setUndoStackObject] = useState({});
    const { setScrollToBomLine, scrollToBomLine } = useRightWindowStore();
    const [openNoBuyerSettingPopup, setOpenNoBuyerSettingPopup] = useState(false);

    const isPurchaseOrderPage = componentType === orderType.PO || location.pathname === routes.createPoPage;
    const isQuotePage = componentType === orderType.QUOTE || location.pathname === routes.quotePage;
    const isOrderManagementPage = componentType === 'ORDER' || location.pathname === routes.orderManagementPage;

    const lastModifiedBomRef = useRef(null);
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const analyticRef = useRef();
    const [maxPageToLoadInViewMode, setMaxPageToLoadInViewMode] = useState(0);

    const setViewIndex = useBomReviewStore((state) => state.setViewIndex);
    // const [viewIndex, setViewIndex] = useState(0);
    const viewIndex = useBomReviewStore((state) => state.viewIndex);
    const achCredit = {
        title: 'Cash In Advance',
        value: 'ach_credit',
        available: true,
        changeTitle: <span className={'w100'}>Cash In Advance</span>
    };
    const bnplCredit = {
        title: 'Net 30 Terms',
        value: 'bryzos_pay',
        available: true,
        changeTitle: <span className={'w100'} onClick={() => { setValue('payment_method', 'bryzos_pay'); navigateToSettings(); }} >Net 30 Terms (Setup)</span>
    }
    const card = {
        title: 'Credit / Debit Card',
        value: purchaseOrder.paymentMethodCard,
        available: true,
        disabled:true,
        changeTitle: <span className={'w100'} onClick={() => { setValue('payment_method', purchaseOrder.paymentMethodCard); navigateToSettings(); }} >Credit / Debit Card (Setup)</span>
    }
    const [paymentMethods, setPaymentMethods] = useState([{ ...achCredit }, { ...bnplCredit }]);
    const [pageIndex, setPageIndex] = useState(0);
    const [filteredFields, setFilteredFields] = useState<any>([]);
    const [initialData, setInitialData] = useState(null);
    // Flag to prevent infinite loops when programmatically setting scrollTop
    const startIndex = pageIndex * 20;
    const endIndex = (pageIndex + 1) * 20;
    let viewLineStatusIndex = 0;
    const [formErrors, setFormErrors] = useState<FormErrors>({});
    const lineItemsToLoad = 20;
    const itemsToAddOnScroll = lineItemsToLoad / 4;
    const [createPoResultCopy, setCreatePoResultCopy] = useState<any>({});
    const [pageCount, setPageCount] = useState(0);
    const [disableReviewCompleteButton, setDisableReviewCompleteButton] = useState(true);
    const [didScrollToPreviousSavedBomLine, setDidScrollToPreviousSavedBomLine] = useState(false);
    const [isCartValid, setIsCartValid] = useState(true);
    const [isAllCartDataLoaded, setIsAllCartDataLoaded] = useState(false);
    const [sendInvoicesToEmailData, setSendInvoicesToEmailData] = useState('');
    const [todayDate, setTodayDate] = useState(new Date());
    const { setPdfUrl, setBomUploadID, setPdfFileName } = useBomPdfExtractorStore();
    const saveBuyerCheckout = useBuyerCheckOutNode();
    const postDraftPo = usePostDraftPo();
    const queryClient = useQueryClient();
    const [disableCloseAnalytic, setDisableCloseAnalytic] = useState(true);
    const [lineSessionId, setLineSessionId] = useState<any>([]);
    const [debouncedSearchString, cancelDebouncedSearchString] = useDebouncedValue(searchStringData, 400);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [lineSessionIdChanges, setLineSessionIdChanges] = useState<string | null>(null);
    analyticRef.current = disableCloseAnalytic && !resetHeaderConfig;
    const createPoResultCopyLength = Object.keys(createPoResultCopy || {})?.length || 0;
    const [rowHeight, setRowHeight] = useState(182);
    const [selectedFilterOption, setSelectedFilterOption] = useState('ALL');
    const isCheckingOut = useRef(false);

    const isSeller = userData?.data?.type === userRole.sellerUser;

    const isBuyerDeleteOrderPage = location.pathname === routes.buyerDeleteOrderPage;

    const {mutateAsync: getOrderLines} = useGetOrderLines();

    // Simple flag to track if saveLocalChanges is running
    const isSavingRef = useRef(false);

    const {
        control,
        register,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        handleSubmit,
        errors
    } = useGenericForm(bomReviewSchema, {
        defaultValues: {
            'cart_items': [],
            'freight_term': "Delivered",
            'isEdit': isOrderManagementPage ? isEditingPo : true
        }
    });



    const setDefaultPaymentOption = (buyingPreference: any, paymentMethods:any, initializeDefaultPayment: boolean) => {
        const payments = [...paymentMethods];
        if (buyingPreference.ach_credit?.is_approved === 1) {
          payments[0].available = false;
        }
        if (buyingPreference.bnpl_settings?.is_approved === 1) {
          payments[1].available = false;
          payments[1].changeTitle = <span>Net 30 Terms</span>
        }else if(buyingPreference.bnpl_settings?.is_approved === null){
            const requestedIncreaseCredit = formatCurrency(parseFloat(buyingPreference.bnpl_settings.requested_credit_limit))
            payments[1].changeTitle = <div className='paymentPending'><span>Net 30 Terms</span> <i> (Request Pending {requestedIncreaseCredit}) </i></div>
        } else if(bnplCredit){
          payments[1] = bnplCredit;
        }

        // if(buyerSetting?.card && buyerSetting?.card?.id){
        //     payments[2].available = false;
        //     payments[2].changeTitle = <span>Credit / Debit Card</span>
        // }

        // setPaymentMethods(payments);
        if(initializeDefaultPayment){
          if (buyingPreference.default_payment_method === 'ACH_CREDIT') {
              setValue('payment_method', 'ach_credit')
              setValue('deposit_amount',0);
          } else if (buyingPreference.default_payment_method === 'BUY_NOW_PAY_LATER') {
              setValue('payment_method', 'bryzos_pay')
          }
        }
      }


    const { fields, append, remove } = useFieldArray({
        control,
        name: "cart_items"
    });

    useEffect(() => {
        setIsCreatePoDirty(false);
        
        setTimeout(() => {
            const element = document.getElementById('createPoTile-0');
            if (element) {
                const styles = getComputedStyle(element);
                const marginTop = parseFloat(styles.marginTop) || 0;
                const marginBottom = parseFloat(styles.marginBottom) || 0;

                const totalHeight = element.offsetHeight + marginTop + marginBottom;
                setRowHeight(totalHeight);
            }
        }, 200)
        return () => {
            handleResetCreatePo();
            if(!isCheckingOut.current && !isOrderManagementPage){
                setSelectedQuote(null);
            }
            setDraftOrderListFromSocket([]);
            setIsEditingPo(false);
            setSelectedCancelLines([]);
        }
    }, [])
    useEffect(() => {
        if (createPoContainerRef.current) {
            if (pageIndex > 0) {
                createPoContainerRef.current.style.overflowY = 'hidden';
            } else {
                createPoContainerRef.current.style.overflowY = 'auto';
            }
        }
    }, [pageIndex])

    useEffect(() => {
        if (createPoContainerRef.current) {
            if (!hidePoLineScroll) {
                if (addPoLineTableRef.current?.scrollTop > 100) {
                    createPoContainerRef.current.style.overflowY = 'hidden';
                } else if (pageIndex === 0) {
                    createPoContainerRef.current.style.overflowY = 'auto';
                }
                scrollerRef.current?.updateScrollPosition(createPoContainerRef.current?.scrollTop + rowHeight * 5 * pageIndex + addPoLineTableRef.current?.scrollTop);
            } else {
                createPoContainerRef.current.style.overflowY = 'auto';
            }
        }
    }, [hidePoLineScroll])

    useEffect(() => {
        if (sessionId && !isSeller) {
            const payload = {
                "data": {
                    "session_id": sessionId
                }
            }
            logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload }).catch(err => console.error(err));
        }
        return () => {
            cancelDebouncedSearchString()
            if (analyticRef.current && sessionId && !isSeller) {
                const payload = {
                    "data": {
                        "session_id": sessionId,
                        "close_status": "CANCEL"
                    }
                }
                logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload })
                    .then(() => setCreatePoSessionId(null))
                    .catch(err => console.error(err))
            }
        }
    }, [sessionId])

    // console.log("check currrent bom daa  tata", currentBomData, bomUploadResult, createPoResultCopy, watch())
    // useEffect(() => {
    //     if (currentBomData?.id && createPoResultCopyLength > 0 && !didScrollToPreviousSavedBomLine) {
    //         const _lastModifiedBom = getLastModifiedBom(currentBomData?.id);
    //         setDidScrollToPreviousSavedBomLine(true);
    //         setTimeout(() => {
    //             handleScrollToBomLine(_lastModifiedBom?.lineId, _lastModifiedBom?.input);
    //         }, 500);
    //     }
    // }, [currentBomData, createPoResultCopyLength])

    const checkAtLeastOneApproved = Object.values(createPoResultCopy).some((item: any) => item?.line_status && item.line_status === 'APPROVED')
    const isInvalidBomForm = Object.values(createPoResultCopy).some((item: any, index: number) => {
        // Only process items that have a line_status
        if((selectedQuote?.in_dispute || selectedQuote?.seller_name) && !item?.id && !item?.delivery_date){
            return true;
        }
        if (!item?.line_status){ 
            return formErrors[index]?.qty || formErrors[index]?.product || formErrors[index]?.qtyEmpty
        };
        
        if (item.line_status === 'SKIPPED' || item.line_status === 'DELETED') {
            return false;
        } else if (item.line_status === 'APPROVED') {
            return formErrors[index]?.qty || formErrors[index]?.product || formErrors[index]?.qtyEmpty
        }
        return (
            item.line_status === 'PENDING'
        );
    })
    useEffect(() => {
        setDisableReviewCompleteButton(isInvalidBomForm);
    }, [isInvalidBomForm]);

    // useEffect(() => {
    //     setProps({ ...props, disableReviewCompleteButton, createPoResultCopy });
    // }, [disableReviewCompleteButton, createPoResultCopy])

    const getInitialData = () => {
        if (location.state?.from === 'bomPdfExtractor') {
            const draftId = (uploadBomInitialData?.id && !uploadBomInitialData?.id.includes(noIdGeneric)) ? uploadBomInitialData.id : updatedDraftId?.id;
            if(updatedDraftId){
                setSelectedQuote(updatedDraftId);
            }else if(uploadBomInitialData?.order_type === orderType.QUOTE){
                setSelectedQuote(quoteList.find((quote: any) => quote.id === draftId));
            }else {
                setSelectedQuote(purchasingList.find((purchasing: any) => purchasing.id === draftId));
            }
            return { ...uploadBomInitialData, isEdit: true, id: draftId };
        }
        if(orderManageMentInitialData){   
            return orderManageMentInitialData;
        }
        if (selectedQuote) {
            return selectedQuote;
        }
        return initialData;
    }

    useEffect(() => {
        setIsAllCartDataLoaded(false);
        // setValue('cart_items', []);
        // if (isCreatePOModule) {
        // }
        setTimeout(() => {
            const cart_items = Array(4).fill({ ...emptyCartItem });
            setValue('cart_items', cart_items);
            initializeCreatePOData();
        }, 0)
        reset();
    }, [initializePoHeaderForm]);

    // Use the tracked length in useEffect
    useEffect(() => {
        const formInputGroup = formInputGroupRef.current;
        const pricingExpired = poHeaderFormWatch?.('pricing_expired');
        if(isOrderManagementPage && !isEditingPo){
                setMaxScrollHeight(bomUploadResult?.length * rowHeight + formInputGroup?.scrollHeight);
                return;
        };

        if((!pricingExpired)){
            const tableHeight = rowHeight * ((pageIndex * 5) + 20);
            const minTableHeight = bomUploadResult?.length === 0 ? 0 : rowHeight * bomUploadResult?.length;
            if (!!formInputGroup) {
                setMaxScrollHeight((formInputGroup?.scrollHeight + Math.max(tableHeight, minTableHeight)));
                setTimeout(() => {
                    if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + addPoLineTableRef.current.scrollTop);
                }, 200)
            }
        }else{
            const tableHeight = rowHeight * ((pageIndex * 5) + bomUploadResult?.length);
            if(bomUploadResult?.length <= 20){
                setMaxScrollHeight(formInputGroup?.scrollHeight + tableHeight);
            }
            setTimeout(() => {
                if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + addPoLineTableRef.current.scrollTop);
            }, 200)
        }
        // const addLineContainer = addPoLineTableRef.current;
    }, [pageIndex, bomUploadResult, isEditingPo, rowHeight]);  // Now depends on the tracked length

    useEffect(() => {
        const initializeData = async () => {
            if (initialData) {
                await initializeCreatePOData();
                setMaxPageToLoadInViewMode(Math.floor(initialData?.cart_items?.length / 20));
            }
        }
        initializeData();
    }, [initialData])

    useEffect(() => {
        const initializeData = async () => {
            if (isHeaderDetailsConfirmed) {
                await initializeCreatePOData();
            }
        };

        initializeData();
    }, [isHeaderDetailsConfirmed])

    useEffect(() => {
        if (createPoData) {
            if(setFinalDisputePayload) setFinalDisputePayload(null);
            // initializeCreatePOData();
            setInitialData(structuredClone(createPoData));
            handleResetCartItem();
            if(location.state?.from !== 'duplicatePo'){
                location.state = null;
            }
        }
        return()=>{
            saveLocalChanges();
            setCreatePoData(null);
        }
        
    }, [createPoData])

    useEffect(()=>{
        if(clickedCreateNewButton){
            if(!isOrderManagementPage){
                setSelectedQuote(null);
                handleResetCreatePo();
                saveLocalChanges();
                setUpdatedDraftId(null);
            }
            setClickedCreateNewButton(null);
        }
    },[clickedCreateNewButton])


    const handleResetCreatePo = () => {
        setIsCreatePoDirty(false);
        reset();
        // Also reset the Zustand store to clear any cached props
        // const { resetRightWindowStore } = useRightWindowStore.getState();
        // if(!isOrderManagementPage) resetRightWindowStore();
        setValue('payment_method', null);
        setValue('isEdit', true);
        handleResetCartItem();
        setBomUploadResult(null);
        setCreatePoResultCopy({});
        setShareEmailWindowProps(null);
        setShareEmailType(null);
        location.state = null;
    }


    useEffect(() => {
        if (debouncedSearchString && lineSessionIdChanges) {
            handleCreatePOSearch(searchStringData, null, lineSessionIdChanges)
        }
    }, [debouncedSearchString])

    const handleResetCartItem = () => {
        for (let i = 0; i < (lineItemsToLoad); i++) {
            // append({ ...emptyCartItem })
            setValue(`cart_items.${i}`, { ...emptyCartItem })
        };
        setCreatePoResultCopy({});
        setCurrentBomData(null);
        // setUploadBomInitialData(null);
    }
    useEffect(() => {
        if (currentBomData?.items?.length > 0 && poHeaderFormWatch?.('shipping_details.zip')?.length === 5 && poHeaderFormWatch?.('shipping_details.state_id')) {
            handleBomData(currentBomData);
        }
    }, [currentBomData, poHeaderFormWatch?.('shipping_details.zip'), poHeaderFormWatch?.('shipping_details.state_id')])

    const handleBomData = async(currentBomData: any) => {
        setValue('bom_id', currentBomData.id);

        const cartItem = [];
        const statusCountObj = { ...bomLineStatusCountObjDefault };
        for (let i = 0; i < currentBomData.items.length; i++) {
            const descriptionObj = currentBomData.items[i].selected_products?.length > 0 ? productMapping[currentBomData.items[i].selected_products[0]] || {} : {};
            const productObj = {
                lineStatusIndex: i,
                bom_line_id: currentBomData.items[i].id || i,
                product_id: currentBomData.items[i]?.selected_products?.[0] || null,
                line_status: currentBomData.items[i].status,
                originalStatus: currentBomData.items[i].original_line_status || currentBomData.items[i].status,
                confidence: currentBomData.items[i].confidence,
                product_tag: currentBomData.items[i].product_tag,
                // description: currentBomData.items[i].description,
                extracted_description: currentBomData.items[i].description,
                specification: currentBomData.items[i].specification,
                search_string: currentBomData.items[i].search_string,
                matched_products: currentBomData.items[i].matched_products,
                selected_products: currentBomData.items[i].selected_products,
                current_page: currentBomData.items[i].current_page,
                total_pages: currentBomData.items[i].total_pages,
                product_index: currentBomData.items[i].product_index,
                grade: currentBomData.items[i].grade,
                qty: currentBomData.items[i].qty?.replace(/[\$,]/g, '') || currentBomData.items[i].qty,
                qty_unit: currentBomData.items[i].qty_unit?.toLowerCase(),
                length: currentBomData.items[i].length,
                weight_per_quantity: currentBomData.items[i].weight_per_quantity,
                matched_product_count: currentBomData.items[i].matched_product_count,
                last_updated_product: currentBomData.items[i]?.last_updated_product ?? 0,
                domesticMaterialOnly: currentBomData.items[i]?.domestic_material_only || false,
                descriptionObj: descriptionObj
            }
            if (productObj.line_status === uploadBomConst.lineItemStatus.approved) {
                statusCountObj[uploadBomConst.lineItemStatus.approved]++;
            } else if (productObj.line_status === uploadBomConst.lineItemStatus.pending) {
                statusCountObj[uploadBomConst.lineItemStatus.pending]++;
            } else if (productObj.line_status === uploadBomConst.lineItemStatus.skipped) {
                statusCountObj[uploadBomConst.lineItemStatus.skipped]++;
            } else if (productObj.line_status === uploadBomConst.lineItemStatus.deleted) {
                statusCountObj[uploadBomConst.lineItemStatus.deleted]++;
            }
            cartItem[i] = productObj;
        }
        await handlePriceIntegration(undefined, cartItem);
        setBomUploadResult(cartItem);
        handleFilterFieldsData(0, cartItem);
        setBOMLineStatusCountObj(statusCountObj);
        setCurrentBomData(null);
        setBomData(null);

    }

    const saveLocalChanges = async () => {

        // Set flag to indicate saving is in progress

        try {
            const localQuote = getLocal(localStorageKeys.poQuoting, null);
            const localPurchasing = getLocal(localStorageKeys.poPurchasing, null);
            const localDraftData = localQuote || localPurchasing;

            if (localDraftData && !(isSeller || isBuyerDeleteOrderPage || isOrderManagementPage )) {
                await handleDraftPoSave(localDraftData, postDraftPo, queryClient, initialData);
            }
        } catch (error) {
            console.error('Error in saveLocalChanges:', error);
        }
    }


    const handleSource = () => {
        let source = selectedQuote?.source || ((isPurchaseOrderPage || isOrderManagementPage) ? 'PO' : 'QUOTE');
        if(selectedQuote?.bom_id || watch('bom_id')){
            const bomId = selectedQuote?.bom_id || watch('bom_id');
            if(isQuotePage && source === 'QUOTE' && bomId){
                source = 'BOM-QUOTE';
            } else if (isPurchaseOrderPage){
                if(source === 'PO' && bomId){
                    source = 'BOM-PO';
                } else if((source === 'QUOTE' || source === 'BOM-QUOTE') && (bomId !== watch('bom_id'))){
                    source = 'BOM-PO';
                }
            }
        }
        return source;
    }

    // useEffect(()=>{
    //     if(poHeaderFormWatch?.() && watch('totalPurchase') > 0){
    //         updateLocalStoreQuote();
    //     }
    // },[watch('totalPurchase')])
    const updateLocalStoreQuote = (createPoResultCopyList: any = Object.values(createPoResultCopy)) => {
        let source = handleSource();
        const localQuote = getLocal(localStorageKeys.poQuoting, null);
        // if(isOrderManagementPage){
        //     return;
        // }
        if (selectedQuote) {
            const watchDataExceptCartItems = { ...watch() };
            watchDataExceptCartItems.cart_items = null;
            const poHeaderFormData = poHeaderFormWatch?.();
            const orderTypeData = poHeaderFormData?.order_type || ((isPurchaseOrderPage) ? orderType.PO : orderType.QUOTE);
            let _selectedQuote = {
                ...selectedQuote,
                ...watchDataExceptCartItems,
                buyer_internal_po: poHeaderFormData?.buyer_internal_po || 'Untitled',
                delivery_date: poHeaderFormData?.delivery_date || '',
                delivery_date_offset: poHeaderFormData?.delivery_date_offset || undefined,
                shipping_details: poHeaderFormData?.shipping_details || {},
                order_type: isOrderManagementPage ? orderType.PO : orderTypeData,
                source: source || null,
                ...watchDataExceptCartItems,
                "cart_items": undefined,
                "time_stamp": dayjs().utc().format(dateTimeFormat.isoDateTime),
            }
            _selectedQuote.cart_items = createPoResultCopyList?.length > 0 ? createPoResultCopyList : undefined;
            console.log("selectedQuote @>>>>>>>", _selectedQuote.order_type, );
            if (_selectedQuote.order_type === orderType.QUOTE) {
                setSelectedQuote(_selectedQuote);
                setLocal(localStorageKeys.poQuoting, _selectedQuote);
                const _quoteList = quoteList.map((quote: any) => {
                    if (quote.id === _selectedQuote.id) {
                        return _selectedQuote;
                    }
                    return quote;
                });
                setQuoteList(_quoteList);
            } else if(_selectedQuote.order_type === orderType.PO && !isOrderManagementPage) {
                setSelectedQuote(_selectedQuote);
                setLocal(localStorageKeys.poPurchasing, _selectedQuote);
                const _purchasingList = purchasingList.map((quote: any) => {
                    if (quote.id === _selectedQuote.id) {
                        return _selectedQuote;
                    }
                    return quote;
                });
                setPurchasingList(_purchasingList);
            } else if(isOrderManagementPage && !selectedQuote?.seller_name){
                // setSelectedQuote(_selectedQuote);
                _selectedQuote.id = noIdGeneric + '-' + uuidv4();
                setLocal(localStorageKeys.poPurchasing, _selectedQuote);
                const _orderManagementList = orderManagementData.map((quote: any) => {
                    if (quote.id === _selectedQuote.id) {
                        return _selectedQuote;
                    }
                    return quote;
                });
                // setOrderManagementData(_orderManagementList);
            }
        } else if(!isOrderManagementPage) {
            const watchDataExceptCartItems = { ...watch() };
            watchDataExceptCartItems.cart_items = null;
            const poHeaderFormData = poHeaderFormWatch();
            let initialQuote = {
                "id": noIdGeneric + '-' + uuidv4(),
                "buyer_internal_po": poHeaderFormData?.buyer_internal_po || 'Untitled',
                "delivery_date": poHeaderFormData?.delivery_date || '',
                "shipping_details": poHeaderFormData?.shipping_details || {},
                "created_date": dayjs().utc().format(dateTimeFormat.isoDateTime),
                "cart_items": undefined,
                "order_type": poHeaderFormData?.order_type || ((isPurchaseOrderPage) ? orderType.PO : orderType.QUOTE),
                "source": source || null,
                ...watchDataExceptCartItems
            }
            initialQuote.cart_items = createPoResultCopyList?.length > 0 ? createPoResultCopyList : undefined;
            if(!initialQuote?.id?.includes(noIdGeneric)){
                return;
            }
            if (initialQuote.order_type === orderType.QUOTE) {
                setLocal(localStorageKeys.poQuoting, initialQuote);
                const updatedQuoteList = quoteList.map(quote => quote.id.includes(noIdGeneric) ? initialQuote : quote);
                if (updatedQuoteList.find(quote => quote.id.includes(noIdGeneric))) {
                    setQuoteList(updatedQuoteList);
                } else {
                    setQuoteList([initialQuote, ...quoteList]);
                }
            } else if(initialQuote.order_type === orderType.PO) {
                setLocal(localStorageKeys.poPurchasing, initialQuote);
                const updatedPurchasingList = purchasingList.map(quote => quote.id.includes(noIdGeneric) ? initialQuote : quote);
                if (updatedPurchasingList.find(quote => quote.id.includes(noIdGeneric))) {
                    setPurchasingList(updatedPurchasingList);
                } else {
                    setPurchasingList([initialQuote, ...purchasingList]);
                }
            }
            setSelectedQuote(initialQuote);
        }
    }

    const handleSaveDuplicatePo = () => {
        const watchDataExceptCartItems = { ...watch() };
        watchDataExceptCartItems.cart_items = null;
        watchDataExceptCartItems.id = noIdGeneric + '-' + uuidv4();
        const poHeaderFormData = poHeaderFormWatch();
        let duplicateQuote = {
            // "id": noIdGeneric + '-' + uuidv4(),
            "buyer_internal_po": 'Untitled',
            "delivery_date": '',
            "shipping_details": poHeaderFormData?.shipping_details || {},
            "created_date": dayjs().utc().format(dateTimeFormat.isoDateTime),
            "cart_items": undefined,
            "order_type": orderType.PO,
            "source": 'DUPLICATED-PO',
            ...watchDataExceptCartItems
        }
        duplicateQuote.cart_items = Object.values(createPoResultCopy)?.length > 0 ? Object.values(createPoResultCopy) : undefined;
        setLocal(localStorageKeys.poPurchasing, duplicateQuote);
        // const updatedPurchasingList = purchasingList.map(quote => quote.id.includes(noIdGeneric) ? duplicateQuote : quote);
        // if (updatedPurchasingList.find(quote => quote.id.includes(noIdGeneric))) {
        //     setPurchasingList(updatedPurchasingList);
        // } else {
        //     setPurchasingList([duplicateQuote, ...purchasingList]);
        // }
    }

    const formattedCartItems = (cartItems: any, draftId: boolean = false) => {
        return cartItems
            .filter((item: any) => (item?.id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED"))
            .map((item, index) => {
                if ((item?.id || (item.descriptionObj && Object.keys(item.descriptionObj).length > 0) && item?.line_status !== "SKIPPED")) {
                    const buyerPricingLbKey = `${newPricingPrefix}${priceUnits.lb}`;
                    let formattedQtyUnit = null;
                    let formattedPriceUnit = null;
                    if (draftId && item?.id && !item?.qty_unit) {
                        formattedQtyUnit = initialData?.cart_items[index]?.qty_unit
                    } else {
                        formattedQtyUnit = item?.qty_unit?.toUpperCase();
                    }

                    if (draftId && item?.id && !item?.price_unit) {
                        formattedPriceUnit = initialData?.cart_items[index]?.price_unit
                    } else {
                        formattedPriceUnit = item?.price_unit?.toUpperCase();
                    }
                    let priceListObj: any = {};
                    if (item?.descriptionObj) {
                        Object.keys(item?.descriptionObj).forEach(key => {
                            if (key.includes(newPricingPrefix)) {
                                priceListObj[key.replace(newPricingPrefix, '').toLowerCase()] = item?.descriptionObj[key];
                            }
                        })
                    }
                    const priceList = item?.descriptionObj ? priceListObj : null;
                    const cartItem = {
                        "line_id": index + 1,
                        "description": item?.descriptionObj?.UI_Description ?? null,
                        "qty": (draftId && item?.id && !item?.qty && initialData?.cart_items[index]?.qty) ? initialData?.cart_items[index]?.qty : (item?.qty) ? item?.qty : null,
                        "qty_unit": formattedQtyUnit || null,
                        "product_tag": item?.product_tag ?? null,
                        "product_tag_mapping_id": item?.descriptionObj?.product_tag ?? null,
                        "buyer_price_per_unit": (draftId && item?.id && !item?.buyer_price_per_unit) ? initialData?.cart_items[index]?.price_per_unit : item?.buyer_price_per_unit ?? null,
                        "price_unit": formattedPriceUnit || null,
                        "buyer_line_total": item?.buyer_line_total ?? null,
                        "product_id": (draftId && item?.id && !item?.descriptionObj?.Product_ID) ? initialData?.cart_items[index]?.product_id : item?.descriptionObj?.Product_ID ?? null,
                        "reference_product_id": item?.descriptionObj?.id ?? null,
                        "shape": item?.descriptionObj?.Key2 ?? null,
                        "domestic_material_only": item?.domesticMaterialOnly ?? false,
                        "buyer_calculation_price": item?.buyer_calculation_price ?? null,
                        "line_status": item?.line_status ?? null,
                        "id": draftId ? item?.id : undefined,
                        "is_line_removed": draftId ? (!item?.descriptionObj?.Product_ID ? true : false) : undefined,
                        "prices_list": priceList
                    };
                    return cartItem
                }
                return null
            });
    }



    const initializeCreatePOData = async () => {
        const _intialData = getInitialData();
        try {
            setShowLoader(true);
            setIsAllCartDataLoaded(true);
            let buyingPreference = { ...buyerSetting };
            initializeBnplSettings()
            if(findItemAttention) findItemAttention(_intialData);
            if (_intialData) {
                setValue('id', _intialData?.id ?? '');
                if (_intialData?.cameFromQuote && !isHeaderDetailsConfirmed && initializePoHeaderForm) {
                    handleHeaderDetailsFill()
                } else {
                    if (!isHeaderDetailsConfirmed && initializePoHeaderForm) {
                        await initializePoHeaderForm(_intialData);
                    }

                    if(_intialData?.cart_items?.length > 0 && !_intialData?.cameFromDuplicatePo){
                        setBomUploadResult(_intialData.cart_items);
                        if(! _intialData?.isEdit)
                        setValue('cart_items', _intialData.cart_items);
                    }
                    setValue('payment_method', _intialData?.payment_method ?? '');
                    setValue('sales_tax', _intialData?.sales_tax ?? 0);
                    if(location.state?.from !== 'bomPdfExtractor'){
                        setValue('buyer_po_price', _intialData?.buyer_po_price ?? '0');
                        setValue('total_weight', parseInt(_intialData?.total_weight) ?? 0);
                    }
                    setValue('deposit_amount', _intialData?.deposit_amount ?? '0');
                    setValue('processing_fees', _intialData?.processing_fees || _intialData?.processing_fees || '0');
                    setValue('subscription', _intialData?.subscription ?? '0');

                    if (_intialData?.bom_id) {
                        setValue('bom_id', _intialData?.bom_id ?? '');
                    }
                    setValue('is_draft_po', _intialData?.is_draft_po ?? false);
                    if(_intialData?.seller_name){
                        setValue('seller_name', _intialData?.seller_name);
                    }
                }
                if(_intialData?.seller_po_number || _intialData?.buyer_po_number){
                    if(setFinalDisputePayload) setFinalDisputePayload({po_number: _intialData?.seller_po_number || _intialData?.buyer_po_number});
                }
                if(_intialData?.cameFromDuplicatePo){
                    await handlePriceIntegration(undefined, _intialData?.cart_items)
                    setBomUploadResult(_intialData?.cart_items);
                    handleFilterFieldsData(0, _intialData?.cart_items);
                    calculateMaterialTotalPrice(_intialData?.cart_items);
                    _intialData.cameFromDuplicatePo = false;
                }
            } else {
                setValue('sales_tax', 0);
                setSendInvoicesToEmailData(buyingPreference.send_invoices_to);
            }
            setDefaultPaymentOption(buyingPreference, paymentMethods, false);
            setIsAllCartDataLoaded(true);
        } catch (err) {
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            setShowLoader(false);
            console.error(err)

        } finally {
            // setCreatePoData(null);
            setShowLoader(false);
            if(!(_intialData?.cart_items?.length > 0)){
                setIsDisableLeftPanel(false);
            }
        }
    }

    useEffect(()=>{
        if(poHeaderFormWatch?.('pricing_expired')){
            setValue('isEdit', !poHeaderFormWatch?.('pricing_expired'));
        }else{
            setValue('isEdit', isOrderManagementPage ? isEditingPo : true);
        }
    }, [poHeaderFormWatch?.('pricing_expired')])
    useEffect(() => {
        if (buyerSetting) {
            initializeBnplSettings()
        }
    }, [buyerSetting])
// console.log("check order data dispute dsddddd 33333333 ", orderManageMentInitialData, selectedQuote, createPoResultCopy, bomUploadResult)
    const initializeBnplSettings = () => {
        const { buyerSetting } = useBuyerSettingStore.getState();
        if (buyerSetting?.bnpl_settings) {
            setValue('availableCreditLimit', buyerSetting?.bnpl_settings?.bryzos_available_credit_limit)
            setValue('bnplStatus', buyerSetting?.bnpl_settings?.bnpl_status);
            if (buyerSetting?.bnpl_settings?.requested_credit_limit) {
                setValue('requestedCreditLimit', buyerSetting?.bnpl_settings?.requested_credit_limit)
            } else {
                setValue('requestedCreditLimit', 0)
            }
            setValue('max_restricted_amount', buyerSetting?.bnpl_settings?.max_restricted_amount ?? "0")
        } else {
            setValue('availableCreditLimit', 0)
            setValue('bnplStatus', BNPLSTATUS.REJECTED);
            setValue('requestedCreditLimit', 0)
            setValue('max_restricted_amount', "0")
        }
    }

    const handleHeaderDetailsFill = () => {
        setCameFromSavedBom(true);
        initializePoHeaderForm(initialData);
    }
    const initializeBomTileData = (index: number) => {
        const selectedProduct = watch(`cart_items.${index}.selected_products`);
        if (selectedProduct?.length > 0) {
            const product = productMapping[selectedProduct[0]];
            if (product) {
                setValue(`cart_items.${index}.descriptionObj`, product);
                setValue(`cart_items.${index}.qty_um`, product.QUM_Dropdown_Options?.split(","));

                // Only set qty_unit if 'Ea' is not in QUM options
                const qumOptions = product.QUM_Dropdown_Options?.split(",");
                if (!qumOptions.includes(watch(`cart_items.${index}.qty_unit`))) {
                    setValue(`cart_items.${index}.qty_unit`, qumOptions[0]);
                }
            }
        }
    }

    useEffect(() => {
        if (bomUploadResult?.length > 0) {
            let createPoResultCopyObj: any = {};
            for (let i = 0; i < bomUploadResult.length; i++) {
                setDisableBidBuyNow(true);
                if (i < lineItemsToLoad) {
                    setValue(`cart_items.${i}`, { ...bomUploadResult[i] });
                    // append({ ...bomUploadResult[i] })
                }
                initializeBomTileData(i)
                validateSavedBomCreatePo(bomUploadResult[i])
                createPoResultCopyObj[i] = bomUploadResult[i];
            }
            if (bomUploadResult.length > lineItemsToLoad) {
                setPageCount(Math.ceil((bomUploadResult.length - lineItemsToLoad) / (lineItemsToLoad / 4)));
            } else {
                setPageCount(0);
            }
            setCreatePoResultCopy(createPoResultCopyObj);
            if(location.state?.from === 'bomPdfExtractor' && selectedQuote?.id){
                
                updateLocalStoreQuote(Object.values(createPoResultCopyObj));
                // saveLocalChanges();
            }
            setIsDisableLeftPanel(false);

        }
    }, [bomUploadResult])

    useEffect(() => {
        if (isAllCartDataLoaded && bomUploadResult?.length === 0) {
            // setValue('cart_items', []);
            setTodayDate(new Date());
            setTimeout(() => {
                for (let i = 0; i < (lineItemsToLoad); i++) {
                    // append({ ...emptyCartItem })
                    setValue(`cart_items.${i}`, { ...emptyCartItem })
                }
            }, 10)
        }
        // const cart_items = Array(lineItemsToLoad).fill({ ...emptyCartItem });
        // setCreatePoResultCopy(cart_items);
    }, [isAllCartDataLoaded])

    
    useEffect(() => {
        const getLocalQuote = getLocal(localStorageKeys.poQuoting, null);
        if (draftOrderListFromSocket?.length > 0 && selectedQuote?.id) {
            if (isPurchaseOrderPage || isQuotePage) {
                const selectedQuoteFetchLatestData = draftOrderListFromSocket.find((item: any) => item.id === selectedQuote?.id);
                if (!selectedQuoteFetchLatestData) setDraftOrderListFromSocket([]);
                if (selectedQuoteFetchLatestData?.id && selectedQuoteFetchLatestData?.id === selectedQuote?.id) {
                    setDraftOrderListFromSocket([]);
                    handleUpdateSelectedData(selectedQuoteFetchLatestData);
                }
                if (isConvertingToPo && !selectedQuoteFetchLatestData && selectedQuote?.id?.includes(noIdGeneric) && isConvertingToPo === selectedQuote?.id) {
                    setDraftOrderListFromSocket([]);
                    handleUpdateSelectedData(draftOrderListFromSocket[0]);
                }
            } else {
                setDraftOrderListFromSocket([]);
            }
        }
        // if (location.state?.from === 'duplicatePo' && !selectedQuote && draftOrderListFromSocket.length === 1) {
        //     setShowLoader(true);
        //     // setSelectedQuote(draftOrderListFromSocket[0]);
        //     handleLoadQuoteData(draftOrderListFromSocket[0]);
        //     setDraftOrderListFromSocket([]);
        //     setValue('total_weight', 0);
        //     location.state.from = null;
        // }
    }, [draftOrderListFromSocket])

    useEffect(()=>{
        if(orderManagementSocketData && orderManagementSocketData?.buyer_po_number === selectedQuote?.buyer_po_number && isOrderManagementPage){
            handleLoadOrderManagementData(orderManagementSocketData);
            setOrderManagementSocketData(null);
        }
    },[orderManagementSocketData])

    
    const handleLoadOrderManagementData = async (item: any) => {
        setShowLoader(true);
        setIsCreatePoDirty(false);
        const orderData = await getOrderLines(item.id);
        // const draftLines = item?.items || [];
        updateOrderManagementData(item, orderData?.data);
        // if(isEditingPo){
        //     showCommonDialog(null, "Order data has been updated.", null, resetDialogStore, [
        //         {
        //             name: 'OK',
        //             action: () => {
        //                 resetDialogStore();
        //             }
        //         }
        //     ]);
        // }
        // setShowLoader(false);
    }

    useEffect(() => {
        if (isOrderManagementPage) {
            setValue('isEdit', isEditingPo);
            if (isEditingPo) {
                handleInitialEditingSetup();
            }

            // setTimeout(() => {
            //     if (addPoLineTableRef.current) {
            //         addPoLineTableRef.current.scrollTop = 0;
            //     }
            // }, 200)

            // handleScrollToBomLine(0);
            // setTimeout(() => {
            //     scrollToTop();
            // }, 600)
        }
    }, [isEditingPo])

    const handleInitialEditingSetup = async() => {
        const orderInitialData = structuredClone(orderManageMentInitialData);   
        await handlePriceIntegration(undefined, orderInitialData?.cart_items)
        setBomUploadResult(orderInitialData?.cart_items);
        handleFilterFieldsData(0, orderInitialData?.cart_items);
        if(!orderInitialData?.seller_name) {
            updateLocalStoreQuote(orderInitialData?.cart_items);
        }
    }
    // useEffect(() => {
    //     if (createPoResultCopyLength > 0) {
    //         setShowLoader(false);
    //     }
    // }, [createPoResultCopyLength])

    // useEffect(() => {
    //     if (createPoResultCopyLength > 0) {
    //         handleFilterFieldsData(0, true);
    //         setPageIndex(0);
    //         setIsProgrammaticScroll(true);
    //         setTimeout(() => {
    //             if (createPoContainerRef.current?.scrollTop < 230 && addPoLineTableRef.current) {
    //                 addPoLineTableRef.current.style.overflowY = 'hidden';
    //             }
    //             addPoLineTableRef.current?.scrollTo({ top: 0 });
    //             setIsProgrammaticScroll(false);
    //         }, 200)

    //     }
    // }, [bomSummaryViewFilter])

    const getFilteredFieldsData = (checkAtLeastOneValid: boolean = true) => {
        // const filteredFieldsData = Object.values(createPoResultCopy).filter((item: any, index: number) => {
        //     const line_status = item?.line_status
        //     const checkValidation = ((bomSummaryViewFilter === 'all') || (bomSummaryViewFilter === 'red' && line_status === uploadBomConst.lineItemStatus.pending) || (bomSummaryViewFilter === 'green' && line_status !== uploadBomConst.lineItemStatus.pending))
        //     return checkValidation;
        // })
        // setFilteredFields(filteredFieldsData);
        const formInputGroup = formInputGroupRef.current;
        const tableHeight = rowHeight * createPoResultCopyLength;
        // setMaxScrollHeight((formInputGroup?.scrollHeight + tableHeight));
        return createPoResultCopy;
    }

    const handleFilterFieldsData = (nextIndex: number = pageIndex, list: any = createPoResultCopy, selectedOption: string) => {
        // const filteredFieldsData = getFilteredFieldsData();
        // const pageCount = filteredFieldsData.length <= lineItemsToLoad ? 0 : Math.ceil((filteredFieldsData.length - lineItemsToLoad) / (lineItemsToLoad / 4));
        // setPageCount(pageCount);
        const filterBy = selectedOption || selectedFilterOption;
        let filterList: any = {};
        if(filterBy !== 'ALL'){
            for (const key in list) {
                const counterList = list[key]?.line_dispute_counter;
                const len = counterList?.length;
                if(counterList?.[len - 1]?.counter_status === filterBy){
                    filterList[key] = list[key];
                }
            }
        }
        else filterList = list;
        const nextList = [];
        for (let i = 0; i < lineItemsToLoad; i++) {
            const index = nextIndex * itemsToAddOnScroll + i;
            const item = filterList[index] ? { ...filterList[index] } : { ...emptyCartItem };
            // if (!isEditingPo && isOrderManagementPage && filterList[index]) {
            //     nextList.push(item);
            // } else {
            if(filterBy === 'ALL' || (filterBy !== 'ALL' && filterList[index])) nextList.push(item);
            // }
        }
        // const multiplier = isAtBottom ? 1 : -1;
        // for(let i = 0; i < itemsToAddOnScroll; i++){
        //     let index;
        //     if(!isAtBottom) index = ((pageIndex * itemsToAddOnScroll) - itemsToAddOnScroll + i);
        //     else index = (pageIndex * itemsToAddOnScroll) + lineItemsToLoad + i;
        //     nextList[i] = createPoResultCopy[index] ? {a:index,...createPoResultCopy[index]} : {a:index,...emptyCartItem}
        // }

        // if(isAtBottom){
        //     list = watch('cart_items').slice(5,20).concat(nextList);
        // }else{
        //     list = nextList.concat(watch('cart_items').slice(0, 15));
        // }
        // console.log("list >>>>>>.>> ", list)
        setValue('cart_items', nextList);
        // if (adjustScroll) {
        //     if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current?.scrollTop);
        // }
    }


    // useEffect(() => {
    //     if (location.pathname === routes.savedBom && (createPoDataFromSavedBom || bomProductMappingDataFromSavedBom)) {
    //         reset();
    //         setViewIndex(0);
    //         setBomSummaryViewFilter('all')
    //         setInitialData(null);
    //         setBomProductMappingSocketData(null);
    //         setScrollToBomLine(null)
    //         if (bomProductMappingDataFromSavedBom) {
    //             setInitialData({ ...bomProductMappingDataFromSavedBom });
    //             setCurrentBomData({ ...bomProductMappingDataFromSavedBom });
    //             setPdfFileName(bomProductMappingDataFromSavedBom?.actual_file_name);
    //             setPdfUrl(bomProductMappingDataFromSavedBom?.s3_url);
    //             setBomUploadID(bomProductMappingDataFromSavedBom?.id);
    //             let formattedUploadBomHeaderData = {
    //                 delivery_date: bomProductMappingDataFromSavedBom?.delivery_date,
    //                 shipping_details: bomProductMappingDataFromSavedBom?.shipping_details,
    //                 order_type: bomProductMappingDataFromSavedBom?.type,
    //                 internal_po_number: bomProductMappingDataFromSavedBom?.title,
    //                 bom_id: bomProductMappingDataFromSavedBom?.id,
    //             }
    //             setUploadBomInitialData({ ...formattedUploadBomHeaderData })
    //         } else if (createPoDataFromSavedBom) {
    //             handleCreatePoDataFromSavedBom();
    //         }
    //     }
    // }, [location.pathname, createPoDataFromSavedBom, bomProductMappingDataFromSavedBom])

    // const handleCreatePoDataFromSavedBom = () => {
    //     const _createPoDataFromSavedBom = { ...createPoDataFromSavedBom };
    //     const cartItem = _createPoDataFromSavedBom?.result?.map((item, i) => ({
    //         bom_line_id: item.id || i,
    //         line_status: item.status,
    //         originalStatus: item.original_line_status || item.status,
    //         confidence: item.confidence,
    //         product_tag: item.product_tag,
    //         description: item.description,
    //         specification: item.specification,
    //         search_string: item.search_string,
    //         matched_products: item.matched_products,
    //         selected_products: item.selected_products,
    //         current_page: item.current_page,
    //         total_pages: item.total_pages,
    //         product_index: item.product_index,
    //         grade: item.grade,
    //         qty: item.qty,
    //         qty_unit: item.qty_unit,
    //         length: item.length,
    //         weight_per_quantity: item.weight_per_quantity,
    //         matched_product_count: item.matched_product_count,
    //         price_unit: item.price_unit,
    //         price: item.price_per_unit,
    //         line_weight: item.line_weight ?? '0.00',
    //         extended: item?.buyer_line_total ? parseFloat(parseFloat(item.buyer_line_total).toFixed(2)) : 0,
    //         domesticMaterialOnly: item?.domestic_material_only || false,
    //         product_id: item.product_id,
    //         draft_line_id: item.id
    //     }));

    //     if (cartItem) {
    //         cartItem.forEach((item: any, index: number) => {
    //             if (item?.product_id) {
    //                 const product = productMapping[item.product_id];
    //                 if (product) {
    //                     item.descriptionObj = product;
    //                 }
    //             } else if (item?.selected_products?.length) {
    //                 const selectedProducts = item.selected_products;
    //                 const hasSelectedProducts = selectedProducts.length > 0;
    //                 if (hasSelectedProducts) {
    //                     const product = productMapping[selectedProducts[0]];
    //                     // Directly set the values on the cartItem object
    //                     item.descriptionObj = product;
    //                 }
    //             }
    //         });
    //     }
    //     setValue('cart_items', cartItem);
    //     setInitialData({ ...createPoDataFromSavedBom, cart_items: cartItem });
    // }

    const getLastModifiedBom = (key: string): string | null => {
        try {
            const lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
            if (lastModifiedBom) {
                const lastModifiedBomData = JSON.parse(lastModifiedBom);
                if (key in lastModifiedBomData) {
                    return lastModifiedBomData[key];
                } else {
                    return null;
                }
            } else {
                return null;
            }
        } catch (e) {
            console.warn('Error checking key in localStorage', e);
            return null;
        }
    };

    const validateSavedBomCreatePo = useCallback((item: any) => {
        // const item = watch(`cart_items.${index}`);
        let descriptionObj = item?.descriptionObj;
        if (item?.selected_products?.length && !descriptionObj) {
            const selectedProducts = item.selected_products;
            const hasSelectedProducts = selectedProducts.length > 0;
            if (hasSelectedProducts) {
                const product = productMapping[selectedProducts[0]];
                // Directly set the values on the cartItem object
                descriptionObj = product;
            }
        }
        if(item.line_cancel_date) return;
        if (descriptionObj && Object.keys(descriptionObj).length > 0) {
            const _selected = descriptionObj;

            if (_selected) {
                const qtyVal = +item.qty || 0;
                const qtyUnit = item.qty_unit;
                const unit = (qtyUnit || (_selected.QUM_Dropdown_Options ? _selected.QUM_Dropdown_Options?.split(",")[0] : '')).toLowerCase();
                const updatedUnit = unit;
                const orderIncrement = getValUsingUnitKey(_selected, updatedUnit, orderIncrementPrefix);
                if (qtyVal && orderIncrement) {
                    // console.log("qtyVal", qtyVal, orderIncrement);
                    if (qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0) {
                        setFormErrors(prevErrors => {
                            const newErrors = { ...prevErrors };
                            delete newErrors[item.lineStatusIndex];
                            return newErrors;
                        });

                        // clearErrors(`cart_items.${index}.qty`);
                        // trigger(`cart_items.${index}.qty`);
                        return true;
                    } else {
                        if (_selected && (item.line_status === uploadBomConst.lineItemStatus.approved || item.line_status == uploadBomConst.lineItemStatus.pending)) {
                            setFormErrors(prevErrors => ({
                                ...prevErrors,
                                [item.lineStatusIndex]: {
                                    qty: true,
                                    product: false,
                                    qtyEmpty: false
                                }
                            }));
                        }
                        // if (_selected) setError(`cart_items.${index}.qty`, { message: `Quantity can only be multiples of ${orderIncrement}` }, { shouldFocus: false })

                        // setValue(`cart_items.${index}.extended`, 0);
                        // setValue(`cart_items.${index}.seller_extended`, 0);
                    }
                }
                else {
                    setFormErrors(prevErrors => ({
                        ...prevErrors,
                        [item.lineStatusIndex]: {
                            qty: false,
                            product: false,
                            qtyEmpty: true
                        }
                    }));
                }
            }
        } else {
            setFormErrors(prevErrors => ({
                ...prevErrors,
                [item.lineStatusIndex]: {
                    qty: false,
                    product: true,
                    qtyEmpty: false
                }
            }));
        }
    }, [watch, getValues, productMapping, setValue]);

    const focusChildElement = (parentElement: HTMLElement | any, elementToFocus?: string | null) => {
        if (!elementToFocus) return;
        if (elementToFocus === "qty") {
            const childElement = parentElement.querySelector('[id^="cart_items."]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if (elementToFocus === "product_tag") {
            const childElement = parentElement.querySelector('[name^="cart_items."][name$=".product_tag"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        } else if(elementToFocus === "descriptionObj") {
            const childElement = parentElement.querySelector('[id^="productDescription-"]');
            if (childElement) {
                setTimeout(() => {
                    (childElement as HTMLElement).focus({ preventScroll: true });
                }, 700);
            }
        };
    }

    const handleLineStatusChange = () => {
        if (bomSummaryViewFilter !== 'all') {
            const filteredFieldsData = getFilteredFieldsData(false);
            const list = filteredFieldsData.slice(pageIndex * 5, pageIndex * 5 + 20)
            if (list.length === 0 && pageIndex === 0) {
                setBomSummaryViewFilter('all');
            } else {
                setValue(`cart_items`, filteredFieldsData.slice(pageIndex * 5, pageIndex * 5 + 20));
            }
        }
    }

    const scrollBomLineToSpecificIndex = (bomLineId: string, elementToFocus?: string | null) => {
        const scrollToElement = (table: HTMLElement, element: HTMLElement) => {
            const elementOffsetTop = element.offsetTop;
            setIsProgrammaticScroll(true);
            table.scrollTo({ top: elementOffsetTop - 50 });
            setTimeout(() => {
                setIsProgrammaticScroll(false);
            }, 200)
        };
        const table = addPoLineTableRef.current;
        const element = document.getElementById(bomLineId);

        if (table && element) {
            scrollToElement(table, element);
            focusChildElement(element, elementToFocus);
        }
        setScrollToBomLine(null)
    }

    // useEffect(() => {
    //     if (scrollToBomLine) {
    //         handleScrollToBomLine(scrollToBomLine, "description")
    //     }
    // }, [scrollToBomLine])
    // console.log("filteredFields", filteredFields);

    const handleScrollToBomLine = async(index: number, elementToFocus?: string | null, isDragging: boolean = false) => {
        const bomList = Object.values(createPoResultCopy);
        // const index = i;
        if (index !== -1) {
            const _pageIndex = (index + 1) - 15 <= 0 ? 0 : Math.ceil(((index + 1) - 15) / 5) + 1;
            setPageIndex(_pageIndex);
            const nextList = [];
            for (let i = 0; i < 20; i++) {
                const lineItemIndex = _pageIndex * 5 + i;
                nextList[i] = createPoResultCopy[lineItemIndex] ? { ...createPoResultCopy[lineItemIndex] } : { ...emptyCartItem }
            }
            setValue('cart_items', nextList);
            setIsProgrammaticScroll(true);
            if (!isDragging) scrollPoHeaderToBottom()
            setTimeout(() => {
                if (!isDragging && createPoContainerRef.current) {
                    const element = document.getElementById(`createPoTile-${index}`);
                    const position = createPoContainerRef.current.scrollTop + _pageIndex * rowHeight * 5 + element?.offsetTop - 200;
                    if (scrollerRef.current) scrollerRef.current.updateScrollPosition(position);
                }
                scrollBomLineToSpecificIndex(`createPoTile-${index}`, elementToFocus);
                setTimeout(() => {
                    setIsProgrammaticScroll(false);
                }, 200)
            }, 500)
        }
    }

    const handleScrollToDisputeLine = async(index: number, elementToFocus?: string | null, isDragging: boolean = false) => {
        const bomList = Object.values(createPoResultCopy);
        // const index = i;
        if (index !== -1) {
            const _pageIndex = (index + 1) - 15 <= 0 ? 0 : Math.ceil(((index + 1) - 15) / 5) + 1;
            setPageIndex(_pageIndex);
            const nextList = [];
            for (let i = 0; i < 20; i++) {
                const lineItemIndex = _pageIndex * 5 + i;
                nextList[i] = createPoResultCopy[lineItemIndex] ? { ...createPoResultCopy[lineItemIndex] } : { ...emptyCartItem }
            }
            setValue('cart_items', nextList);
            setIsProgrammaticScroll(true);
            if (!isDragging) scrollPoHeaderToBottom()
            setTimeout(() => {
                if (!isDragging && createPoContainerRef.current) {
                    const element = document.getElementById(`disputeTile-${index}`);
                    const position = createPoContainerRef.current.scrollTop + _pageIndex * rowHeight * 5 + element?.offsetTop - 200;
                    if (scrollerRef.current) scrollerRef.current.updateScrollPosition(position);
                }
                scrollBomLineToSpecificIndex(`disputeTile-${index}`, elementToFocus);
                setTimeout(() => {
                    setIsProgrammaticScroll(false);
                }, 200)
            }, 500)
        }
    }


    

    const handleLoadNextItems = (index: number, isAtBottom: boolean, updateScroll: boolean = true) => {
        const { scrollTop } = addPoLineTableRef.current;
        handleFilterFieldsData(index);
        setPageIndex(index);
        setIsProgrammaticScroll(true);
        setTimeout(() => {
            if (addPoLineTableRef.current) addPoLineTableRef.current.scrollTop = isAtBottom ? scrollTop - rowHeight * 5 : scrollTop + rowHeight * 5;
            // handleViewIndexChange();
        }, 100)
        if (scrollerRef.current && updateScroll) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + scrollTop);
        // Reset flag after a short delay
        setTimeout(() => {
            setIsProgrammaticScroll(false);
        }, 200);
    }

    const handleLineItemScroll = () => {
        // Skip if the scroll is programmatic
        if (!addPoLineTableRef.current || isProgrammaticScroll) return;
        const { scrollTop, scrollHeight, clientHeight } = addPoLineTableRef.current;
        const isAtBottom = Math.abs(scrollHeight - scrollTop - clientHeight) < 5; // 5px threshold
        // Unfocus any focused elements when scrolling
        const focusedElement = document.activeElement;
        if (focusedElement instanceof HTMLElement) {
            // Check if element is out of view
            const rect = focusedElement.getBoundingClientRect();
            const isOutOfView = (
                rect.bottom < 0 ||
                rect.top > window.innerHeight ||
                rect.right < 0 ||
                rect.left > window.innerWidth
            );

            if (isOutOfView) {
                focusedElement.blur();
            }
        }
        if (isAtBottom) {
            if((isOrderManagementPage && !isEditingPo) || (!isOrderManagementPage && !watch('isEdit')))return;
            handleLoadNextItems(pageIndex + 1, true, false)
            return;
        }
        else if (pageIndex > 0 && scrollTop < rowHeight) {
            handleLoadNextItems(pageIndex - 1, false, false)
            return;
        }
        if (scrollerRef.current) scrollerRef.current.updateScrollPosition(createPoContainerRef.current.scrollTop + pageIndex * rowHeight * 5 + scrollTop);
        // else {
        //     handleViewIndexChange();
        // }    
    }

    // const handleViewIndexChange = () => {
    //     const container = addPoLineTableRef.current;
    //     if (container) {
    //         const firstRow = container.querySelector('tbody tr:first-child');
    //         if (firstRow) {
    //             const { scrollTop } = container;
    //             const rect = firstRow.getBoundingClientRect();

    //             // Get computed styles to access margins
    //             const computedStyle = window.getComputedStyle(firstRow);

    //             // Calculate total height including margins
    //             const marginTop = parseInt(computedStyle.marginTop, 10);
    //             const marginBottom = parseInt(computedStyle.marginBottom, 10);

    //             // Total height = height + margin-top + margin-bottom
    //             const firstRowHeight = rect.height + marginTop + marginBottom;
    //             const index = Math.floor(scrollTop / firstRowHeight);

    //             // // Clear existing timeout
    //             // if (debounceTimeoutRef.current) {
    //             //     clearTimeout(debounceTimeoutRef.current);
    //             // }

    //             // // Set new timeout to debounce the setViewIndex call
    //             // debounceTimeoutRef.current = setTimeout(() => {
    //             // }, 400);
    //             setViewIndex(pageIndex * 5 + index);


    //         } else {
    //             console.warn('First row not found');
    //         }
    //     }
    // };

    const openAddLineTab = () => {
        if (!addPoLineTableRef.current || !hidePoLineScroll) return;
        const container = addPoLineTableRef.current;
        if (container.scrollTop > 0 && orderInfoIsFilled && createPoContainerRef.current) {
            createPoContainerRef.current.scrollTo({
                top: createPoContainerRef.current.scrollHeight,
                behavior: 'smooth'
            });
            container.style.overflowY = 'auto';
            const formInputHeight = formInputGroupRef.current?.clientHeight;
            setTimeout(() => {
                if (scrollerRef.current) scrollerRef.current.updateScrollPosition(pageIndex * rowHeight * 5 + container.scrollTop + formInputHeight);
            }, 400)
        }
    }

    const saveModifiedBom = (index: number, input: any) => {
        try {
            if (currentBomData?.id && watch(`cart_items.${index}`)?.bom_line_id) {
                const data = {
                    [currentBomData?.id]: { lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input, pageIndex }
                }
                const _lastModifiedBom = localStorage.getItem(localStorageKeys.lastModifiedBom);
                if (_lastModifiedBom) {
                    const _lastModifiedBomData = JSON.parse(_lastModifiedBom);
                    _lastModifiedBomData[currentBomData?.id] = { lineId: watch(`cart_items.${index}`)?.bom_line_id, input: input, pageIndex };
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(_lastModifiedBomData));
                } else {
                    localStorage.setItem(localStorageKeys.lastModifiedBom, JSON.stringify(data));
                }
            }
        } catch (e) {
            console.warn('Could not store value in localStorage', e);
        }
    }

    const handleScrollerDrag = (newScrollPosition: number) => {
        setIsProgrammaticScroll(true);
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
        }
        const indexBasedOnScrollPosition = Math.ceil((newScrollPosition - createPoContainerRef.current.scrollTop) / rowHeight);
        if (newScrollPosition >= 0 && newScrollPosition < 204) {
            // debounceTimeoutRef.current = setTimeout(() => {
            createPoContainerRef.current.scrollTop = newScrollPosition;
            handleScrollToBomLine(0, null, true);
            setIsProgrammaticScroll(false);
            // }, 200)
        }
        else if (scrollerRef.current?.isAtBottom(100)) {
            // createPoContainerRef.current.scrollTop = newScrollPosition;
            // Set new timeout to debounce the setViewIndex call
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                handleScrollToBomLine(indexBasedOnScrollPosition, null, true);
            }, 200);
        }
        else if (newScrollPosition >= 204) {

            // Set new timeout to debounce the setViewIndex call
            // console.log("newScrollPosition", newScrollPosition, maxScrollHeight);
            debounceTimeoutRef.current = setTimeout(() => {
                createPoContainerRef.current.scrollTop = newScrollPosition;
                // const index = Math.floor((newScrollPosition - 162) / rowHeight);
                // const bomLineId = createPoResultCopy[index]?.bom_line_id;
                handleScrollToBomLine(indexBasedOnScrollPosition, null, true);
            }, 200);
        }

        // if(newScrollPosition >= 0 && newScrollPosition < 204){
        //     headerScroll = newScrollPosition;
        //     tableScroll = 0;
        //     if(pageIndex !== 0){
        //         setPageIndex(0);

        //     }
        // }else {
        //     headerScroll = 204;
        //     tableScroll = newScrollPosition - 204;
        // }
        // if(createPoContainerRef.current)
        //     createPoContainerRef.current.scrollTop = headerScroll;

        // // Clear existing timeout

        // if(addPoLineTableRef.current)
        //     addPoLineTableRef.current.scrollTop = tableScroll;

    }

    // Validates that cart items have required fields if they have a description
    const validateCart = () => {
        const cartItems = Object.values(createPoResultCopy);
        if (!cartItems?.length) return true;

        // Only validate items that have a description
        const itemsWithDescription = cartItems.filter(item => item?.descriptionObj?.UI_Description && item?.line_status !== "SKIPPED" && item?.line_status !== "DELETED" || item?.line_status === "APPROVED");
        if (!itemsWithDescription.length) return false;

        // Check required fields are present and valid
        return itemsWithDescription.every(item => {
            const quantity = Number(item.qty);
            return quantity > 0 &&
                Boolean(item.qty_unit) &&
                Boolean(item.price_unit);
        });
    };

    // Monitor cart changes and update validation state
    useEffect(() => {
        // const subscription = watch((_, { name }) => {
        //     if (name?.startsWith('cart_items')) {
        //     }
        // });
        setIsCartValid(validateCart());
        // return () => subscription.unsubscribe();
    }, [createPoResultCopy]);



    const navigateToSettings = () => {
        navigate(routes.buyerSettingPage, { state: { tab: 'PAYMENTS' } })
    }

    const { updateLineProduct, calculateMaterialTotalPrice, pricePerUnitChangeHandler, removeLineItem, handleCreatePOSearch, saveUserLineActivity, handlePriceIntegration, updateLineItem, resetPricePerUnitFields, resetQtyAndPricePerUnitFields, willGearUpdate, handleSubmitValidation, formatCartItems, getCartItems, handleUpdateSelectedData, handleLoadQuoteData }
        = useCreatePoPriceCalculation(setValue, clearErrors, userPartData, getValues, remove, sessionId, watch, trigger, setError, initialData, setFormErrors, formErrors, createPoResultCopy, pageIndex, itemsToAddOnScroll, handleFilterFieldsData, setCreatePoResultCopy, poHeaderFormWatch, bnplCredit, setSelectedProduct, updateLocalStoreQuote, isOrderManagementPage);


    const disableFormValidation = !orderInfoIsFilled ||
        !handleSubmitValidation ||
        watch('total_weight') < pricingBrackets?.[0]?.min_weight ||
        !watch('totalPurchase') || disableReviewCompleteButton || !isCartValid;


    // Disable place order button if any validation fails
    const disablePlaceOrderButton = disableFormValidation ||
        poHeaderFormWatch('shipping_details.validating_state_id_zip') || (watch('payment_method') === 'bryzos_pay' && (watch('bnplStatus') === BNPLSTATUS.ON_HOLD || (watch('bnplStatus') === BNPLSTATUS.RESTRICTED && Number(watch(`totalPurchase`)) > Number(watch('max_restricted_amount'))))) ||
        !watch('payment_method') ||
        (getValues('payment_method') === 'bryzos_pay' && (
            buyerSetting?.bnpl_settings?.is_approved === null ||
            watch('totalPurchase') > (watch('availableCreditLimit') || 0)
        ));
    // Disable place order button if any validation fails
    const disableConvertToPoButton = disableFormValidation || poHeaderFormWatch('shipping_details.validating_state_id_zip');

    const getExportPoData = () => {
        const paymentMethod = watch('payment_method') || 'bryzos_pay';
        return { ...watch(), ...poHeaderFormWatch(), selectedOptionPayment: paymentMethod };
    }

    
  const calculateExtendedPrice = async(actualIndex: number) => {
    if(createPoResultCopy[actualIndex]?.descriptionObj){
      const isGearUpdated = await handlePriceIntegration(actualIndex);
      if(isGearUpdated) handleFilterFieldsData();
    }
  }

  const handleUndoForLine = async(lastElement: any) => {
    const index = lastElement.actualIndex;
    await handleScrollToBomLine(index, lastElement.name);
    const _pageIndex = (index + 1) - 15 <= 0 ? 0 : Math.ceil(((index + 1) - 15) / 5) + 1;
    const _index = index - (_pageIndex * 5);
    setValue(`cart_items.${_index}.${lastElement.name}`, lastElement.value);
    if(lastElement.name === "descriptionObj"){
        if(lastElement.value === ''){
            setCreatePoResultCopy(prev => {
                const newCreatePoResultCopy = { ...prev };
                newCreatePoResultCopy[index].descriptionObj = {};
                newCreatePoResultCopy[index].qty = '';
                newCreatePoResultCopy[index].qty_unit = '';
                newCreatePoResultCopy[index].buyer_line_total = 0;
                newCreatePoResultCopy[index].seller_extended = 0;
                newCreatePoResultCopy[index].buyer_price_per_unit = 0;
                newCreatePoResultCopy[index].seller_price = 0;
                return newCreatePoResultCopy;
            });
            updateLineProduct(_index, undefined);
        } else {
            updateLineProduct(_index, lastElement.value);
        }
        return;
    }
    createPoResultCopy[index][lastElement.name] = lastElement.value;
    if (createPoResultCopy[index]?.descriptionObj) {
        const isGearUpdated = await handlePriceIntegration(index, Object.values(createPoResultCopy), _pageIndex);
        if (isGearUpdated) handleFilterFieldsData(_pageIndex, createPoResultCopy);
    }
  }




    const onSubmit = (data) => {
        const date = new Date();

        if(isCheckingOut.current) return;

        if (dayjs(date).format('M/D/YYYY') === dayjs(todayDate).format('M/D/YYYY')) {
            setShowLoader(true);
            const totalPurchaseValue = data.buyer_po_price;
            const poHeaderFormData = poHeaderFormWatch();
            if(poHeaderFormData?.shipping_details?.delivery_address_id){
                let defaultAddress = buyerSetting?.delivery_address?.find((address: any) => address.id === poHeaderFormData?.shipping_details?.delivery_address_id);
                if(defaultAddress){
                    const isAddressChanged = checkAddressChanged(poHeaderFormData.shipping_details, defaultAddress);
                    if(isAddressChanged){
                        poHeaderFormData.shipping_details.delivery_address_id = null;
                    }
                }
            }
            const totalSellerPurchaseValue = parseFloat(data.seller_price).toFixed(2);
            const localDateTime = dayjs().format(dateTimeFormat.isoDateTimeWithTFormat);
            poHeaderFormData.shipping_details.line2 = poHeaderFormData.shipping_details.line2?.trim() || null;
            const payload = {
                "data": {
                    "internal_po_number": poHeaderFormData.buyer_internal_po,
                    "shipping_details": poHeaderFormData.shipping_details,
                    "delivery_date": dayjs(poHeaderFormData.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
                    "payment_method": data.payment_method,
                    "seller_price": totalSellerPurchaseValue,
                    "price": String(totalPurchaseValue),
                    "sales_tax": data.sales_tax,
                    "freight_term": "Delivered",
                    "cart_items": formatCartItems(Object.values(createPoResultCopy)),
                    "checkout_local_timestamp": localDateTime,
                    // "delivery_date_offset": null,
                    "order_type": poHeaderFormData?.order_type ?? '',
                    "order_size": String(data?.total_weight) ?? ''
                }
            };

            if (watch('id') && !watch('id').includes(noIdGeneric)) {
                payload.data.draft_id = watch('id')
            }
            setDisableCloseAnalytic(false)
            isCheckingOut.current = true;
            saveBuyerCheckout.mutateAsync(payload)
                .then(res => {
                    if (res.data.data.error_message) {
                        isCheckingOut.current = false;
                        if(res.data.data.mobile_error_message === "BNPL_NOT_SET_UP"){
                            setOpenNoBuyerSettingPopup(true);
                        }else{
                            showCommonDialog(null, res.data.data.error_message, null, resetDialogStore, [
                                {
                                    name: 'Ok',
                                    action: () => resetDialogStore()
                                }
                            ])
                        }
                        setShowLoader(false);
                        saveUserActivity(null, res.data.data.error_message)
                        setDisableCloseAnalytic(true)
                    } else {
                        queryClient.invalidateQueries([reactQueryKeys.getUserPartData]);
                        // if (watch('bom_id') && !watch('is_draft_po')) {
                        //     setLeftPanelData(leftPanelData.filter((item: any) => item.id !== watch('bom_id')))
                        // } else if (watch('is_draft_po') && watch('bom_id')) {
                        //     setLeftPanelData(leftPanelData.filter((item: any) => item.id !== watch('bom_id')))
                        // }
                        if (watch('id') && !watch('id').includes(noIdGeneric)) {
                            const filterdPurchasingList = purchasingList.filter((item: any) => item.id !== watch('id'));
                            setPurchasingList(filterdPurchasingList);
                        }
                        const payload = {
                            "data": {
                                "session_id": sessionId,
                                "close_status": "ACCEPT"
                            }
                        }
                        logUserActivity.mutateAsync({ url: import.meta.env.VITE_API_SERVICE + '/user/create_po_open_close', payload })
                            .catch(err => console.error(err))
                        clearLocal(localStorageKeys.poPurchasing);
                        // setSelectedQuote({...selectedQuote,buyer_po_number : res.data.data});
                        setOpenOrderFromList(res.data.data)
                        setUpdatedDraftId(null);
                        navigate(routes.orderManagementPage, { state: { isFromCheckout: true, poNumber: res.data.data, jobNumber: poHeaderFormData.buyer_internal_po, sendInvoicesTo: sendInvoicesToEmailData, selectedOptionPayment: data.payment_method, } })
                        // setCreatePoData(null);
                        saveUserActivity(res.data.data, null);
                    }
                })
                .catch(err => {
                    isCheckingOut.current = false;
                    saveUserActivity(null, (err?.message ?? err));
                    setDisableCloseAnalytic(true)
                    showCommonDialog(null, "Something went wrong. Please try again in sometime", null, resetDialogStore, [
                        {
                            name: 'Ok',
                            action: () => resetDialogStore()
                        }
                    ])
                    setShowLoader(false);
                    console.error(err)
                })
        } else {
            showCommonDialog(null, 'Selected delivery date is incorrect, please select correct delivery date.', null, resetDialogStore, [
                {
                    name: 'Ok',
                    action: () => resetDialogStore()
                }
            ])
            setValue('delivery_date', null)
            getDeliveryDateData();
            setTodayDate(new Date());
        }
    }
    
    const handleExitEditModeTableData = async() => {
        setBomUploadResult([]);
        setIsProgrammaticScroll(true);
        setTimeout(() => {
        if(createPoContainerRef.current)createPoContainerRef.current.scrollTop = 0;
        if(addPoLineTableRef.current)addPoLineTableRef.current.scrollTop = 0;
        }, 100)
        setTimeout(() => {
            setIsProgrammaticScroll(false);
        }, 200)
        setPageIndex(0);
        setHidePoLineScroll(false);
        await initializeCreatePOData();
    }

    // Expose orderInfoIsFilled to parent component
    useImperativeHandle(ref, () => ({
        getInitialData,
        handleScrollerDrag,
        disableFormValidation,
        getCartItems,
        getExportPoData,
        watch,
        updateLocalStoreQuote,
        setValue,
        handleUndoForLine,
        handleScrollToBomLine,
        handleScrollToDisputeLine,
        handleSaveDuplicatePo,
        createPoResultCopy,
        handleExitEditModeTableData,
        disableConvertToPoButton,
        handlePriceIntegration
        // reset
    }), [getInitialData, handleScrollerDrag, disableFormValidation, getCartItems, getExportPoData, watch, updateLocalStoreQuote, setValue, handleUndoForLine, handleSaveDuplicatePo, createPoResultCopy, handleExitEditModeTableData, disableConvertToPoButton, handlePriceIntegration]);

    useEffect(() => {
        const orderSummaryProps = {
            watch,
            paymentMethods,
            control,
            setValue,
            getValues,
            navigateToSettings,
            saveUserActivity,
            disablePlaceOrderButton,
            handleSubmit,
            onSubmit,
            disableReviewCompleteButton,
            pricingBrackets,
            isCreatePOModule,
            poHeaderFormWatch,
            disableConvertToPoButton,
            handleDraftPoSave,
            initialData
        };
        const sellerOrderSummaryProps = {
            orderDetail: initialData,
            totalOrderValue: watch('totalPurchase')
        }
        setProps({ ...props, ...orderSummaryProps, ...sellerOrderSummaryProps, componentType, finalDisputePayload });

    }, [
        watch,
        watch('buyer_po_price'),
        watch('sales_tax'),
        watch('deposit_amount'),
        watch('subscription'),
        watch('totalPurchase'),
        watch('availableCreditLimit'),
        watch('payment_method'),
        watch('requestedCreditLimit'),
        watch('total_weight'),
        disablePlaceOrderButton,
        disableReviewCompleteButton,
        currentBomData,
        pricingBrackets,
        isCreatePOModule,
        poHeaderFormWatch,
        disableConvertToPoButton,
        initialData,
        finalDisputePayload,
        createPoResultCopy
    ]);


    const handleCtrlClick = (item: any, onCtrlClickCallback?: (currentSelectedIds: any[], updatedIds: any[]) => void) => {
        const currentSelectedIds = [...selectedCancelLines];
        const itemId = item.id;
        let updatedIds: any[] = [];

        if (currentSelectedIds.includes(itemId)) {
            // Remove item from selection
            updatedIds = currentSelectedIds.filter(id => id !== itemId);
            setSelectedCancelLines(updatedIds);
        } else {
            // Add item to selection
            updatedIds = [...currentSelectedIds, itemId];
            setSelectedCancelLines(updatedIds);
        }
        onCtrlClickCallback && onCtrlClickCallback(currentSelectedIds, updatedIds);
        // setLastClickedIndex(index);
    };
    
    const handleOptionChange = (event: any) => {
        const value = event.target.value;
        setSelectedFilterOption(value);
        handleFilterFieldsData(0, createPoResultCopy, value)
    };

  
    const handleGoToSettings = () => {
        setOpenNoBuyerSettingPopup(false);
        navigate(routes.buyerSettingPage, { state: { tab: 'COMPANY' } })
    }

    return (
        <div data-hover-video-id={componentType === 'QUOTE' ? 'quoting-create-po' : componentType === 'PO' ? 'purchase-create-po': ''} style={{ overflowY: (!hidePoLineScroll && orderInfoIsFilled) ? 'auto' : 'hidden' }} className={clsx(styles.addPoLineTable)} onScroll={handleLineItemScroll} ref={addPoLineTableRef} onClick={() => { openAddLineTab() }}>

                {!!(selectedQuote?.in_dispute && userData?.data?.type === userRole.buyerUser) &&
                <div className={styles.filterHeader}>
                    <div className={styles.filterHeaderDropdownMain}>
                         <div className={styles.filterIconContainer}>
                          <FilterIcon className={styles.filterIcon} />
                         </div>
                         <div className={styles.filterSelectContainer}>
                            <Select
                                value={selectedFilterOption}
                                onChange={handleOptionChange}
                                displayEmpty
                                className={styles.disputeFilterSelect}
                                IconComponent={DropdownIcon}
                                MenuProps={{
                                    PaperProps: {
                                        className: styles.disputeFilterMenu,
                                    },
                                }}
                                sx={{
                                    '& .MuiSelect-select': {
                                        width: '100%',
                                        padding: '0px 0px 0px 22px',
                                        color: '#ffffff',
                                        fontSize: '14px',
                                        fontFamily: 'Inter',
                                        fontWeight: 'normal',
                                        letterSpacing: '0.56px',
                                        backgroundColor: 'transparent',
                                        border: 'none',
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        '&:focus': {
                                            backgroundColor: 'transparent',
                                        }
                                    },
                                    '& .MuiOutlinedInput-notchedOutline': {
                                        border: 'none',
                                    },
                                    '& .MuiSelect-icon': {
                                        color: '#ffffff',
                                        right: '12px',
                                        transform: 'none',
                                        borderLeft: 'solid 1px rgba(255, 255, 255, 0.1)',
                                        height: '100%',
                                        top: 'unset',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    },
                                    '&:hover .MuiOutlinedInput-notchedOutline': {
                                        border: 'none',
                                    },
                                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                                        border: 'none',
                                    }
                                }}
                            >
                                {disputeOptions.map((option) => {
                                    let optionColor = "";
                                    if(option.value === 'RESOLVED'){
                                        optionColor = '#2c8a4a';
                                    }else if(option.value === 'PENDING'){
                                        optionColor = '#bc6d42';
                                    }else if(option.value === 'REJECTED'){
                                        optionColor = '#9d1d20';
                                    }
                                    return(
                                    <MenuItem
                                        key={option.value}
                                        value={option.value}
                                        className={styles.disputeFilterMenuItem}
                                        sx={{
                                            fontSize: '14px',
                                            padding: '8px 16px',
                                            '&:hover': {
                                                backgroundColor: '#3a3a40',
                                            },
                                            '&.Mui-selected': {
                                                backgroundColor: '#2d2d33',
                                                '&:hover': {
                                                    backgroundColor: '#3a3a40',
                                                }
                                            }
                                        }}
                                    >
                                       {option.value === 'ALL' ? '' : <span style={{backgroundColor: optionColor}} className={styles.dot} />} {option.label}
                                    </MenuItem>
                                )})}
                            </Select>
                            </div>
                    </div>
                    </div>
                }
            <table >
                <thead>
                    <tr>
                        <th><span>LN</span></th>
                        <th><span>DESCRIPTION</span></th>
                        <th><span>QTY</span></th>
                        <th><span> $/UNIT</span></th>
                        <th colSpan={2}><span>EXT ($)</span></th>
                    </tr>
                </thead>
                <tbody>
                    {
                        watch('cart_items').map((item: any, _index: number) => {
                            const index = (pageIndex * itemsToAddOnScroll) + _index;
                            viewLineStatusIndex++;
                            return (
                                <React.Fragment key={_index}>
                                    {(item?.line_dispute_counter?.length > 0 && !item?.line_cancel_date)? 
                                        <DisputeTile
                                            index={_index}
                                            actualIndex={index}
                                            register={register}
                                            watch={watch}
                                            setValue={setValue}
                                            currentFocusedItem={currentFocusedItem}
                                            setFinalDisputePayload={setFinalDisputePayload}
                                            finalDisputePayload={finalDisputePayload}
                                            removeFromAttentionItems={removeFromAttentionItems}
                                            isStateZipValChange={isStateZipValChange}
                                            setCreatePoResultCopy={setCreatePoResultCopy}
                                            addToAttentionItems={addToAttentionItems}
                                        /> 
                                        :
                                        <CreatePoTile
                                            componentType={componentType}
                                            index={_index}
                                            actualIndex={index}
                                            register={register}
                                            fields={fields}
                                            // updateValue={updateValue}
                                            updateLineProduct={updateLineProduct}
                                            products={products}
                                            setValue={setValue}
                                            watch={watch}
                                            errors={errors}
                                            control={control}
                                            getValues={getValues}
                                            pricePerUnitChangeHandler={pricePerUnitChangeHandler}
                                            removeLineItem={removeLineItem}
                                            userPartData={userPartData}
                                            sessionId={sessionId}
                                            selectedProduct={selectedProduct}
                                            searchStringData={searchStringData}
                                            setSearchString={setSearchString}
                                            setLineSessionId={setLineSessionId}
                                            lineSessionId={lineSessionId}
                                            handleCreatePOSearch={handleCreatePOSearch}
                                            // apiCallInProgress={apiCallInProgress}
                                            saveUserLineActivity={saveUserLineActivity}
                                            orderInfoIsFilled={orderInfoIsFilled}
                                            setDisableBidBuyNow={setDisableBidBuyNow}
                                            openAddLineTab={openAddLineTab}
                                            setOpenDeliveryToDialog={setOpenDeliveryToDialog}
                                            hidePoLineScroll={hidePoLineScroll}
                                            setHidePoLineScroll={setHidePoLineScroll}
                                            scrollToTop={scrollToTop}
                                            calculateMaterialTotalPrice={calculateMaterialTotalPrice}
                                            setIsCreatePoDirty={setIsCreatePoDirty}
                                            isHeaderDetailsConfirmed={isHeaderDetailsConfirmed}
                                            cameFromSavedBom={cameFromSavedBom}
                                            handlePriceIntegration={handlePriceIntegration}
                                            clearErrors={clearErrors}
                                            updateLineItem={updateLineItem}
                                            createPoResultCopy={createPoResultCopy}
                                            setCreatePoResultCopy={setCreatePoResultCopy}
                                            setFormErrors={setFormErrors}
                                            handleFilterFieldsData={handleFilterFieldsData}
                                            willGearUpdate={willGearUpdate}
                                            handleLoadNextItems={handleLoadNextItems}
                                            pageIndex={pageIndex}
                                            lineItemsToLoad={lineItemsToLoad}
                                            debouncedSearchString={debouncedSearchString}
                                            setSelectedProduct={setSelectedProduct}
                                            lineSessionIdChanges={lineSessionIdChanges}
                                            setLineSessionIdChanges={setLineSessionIdChanges}
                                            formErrors={formErrors}
                                            updateLocalStoreQuote={updateLocalStoreQuote}
                                            handleStoreUndoStack={handleStoreUndoStack}
                                            setUndoStackObject={setUndoStackObject}
                                            undoStackObject={undoStackObject}
                                            calculateExtendedPrice={calculateExtendedPrice}
                                            handleCtrlClick={handleCtrlClick}
                                            selectedCancelLines={selectedCancelLines}
                                            isStateZipValChange={isStateZipValChange}
                                            setIsOrderLineChanges={setIsOrderLineChanges}
                                        />

                                    }
                                </React.Fragment>
                            )
                        })
                    }
                </tbody>
            </table>
            <NoBuyerSettingPopup containerRef={addPoLineTableRef} openNoBuyerSettingPopup={openNoBuyerSettingPopup} setOpenNoBuyerSettingPopup={setOpenNoBuyerSettingPopup} handleGoToSettings={handleGoToSettings} />
        </div>
    )
})

export default CreatePoTable;