version: 2.1

orbs:
  node: circleci/node@5.1.0
  win: circleci/windows@5.0.0

jobs:

  build-macos:
    macos:
      xcode: "14.3.1"
      resource_class: macos.m1.medium.gen1
    environment:
      ENVIRONMENT: << pipeline.parameters.ENVIRONMENT >>
    steps:
      - checkout 
      - run: chmod +x scripts/add-macos-cert.sh && ./scripts/add-macos-cert.sh
      # look for existing cache and restore if found
      - restore_cache:
          key: v1-deps-{{ checksum "package-lock.json" }}
      # install dependencies    
      - run:
          name: install dependencies
          command: npm install
      # save any changes to the cache
      - save_cache:
          key: v1-deps-{{ checksum "package-lock.json" }}
          paths: 
            - node_modules  
      - run:
          name: package and make electron darwin
          command: |
           node_modules/cross-env/src/bin/cross-env.js NODE_ENV=$ENVIRONMENT APPLE_ID=$APPLE_ID APPLE_ID_PASSWORD=$APPLE_ID_PASSWORD APPLE_TEAM_ID=$APPLE_TEAM_ID npm run build:electron:circleci:mac
      - run:
          name: upload files to drive
          command: |
            cd ./scripts/nodeUploader
            echo $DRIVE_CONFIG_BASE64 | base64 --decode >./config.json
            npm i
            node_modules/cross-env/src/bin/cross-env.js NODE_ENV=$ENVIRONMENT DRIVE_FOLDERID=${DRIVE_FOLDERID} npm run upload

      - store_artifacts:
          path: out/make

  build-win:
    executor:
      name: win/default
      shell: bash.exe
    environment:
      ENVIRONMENT: << pipeline.parameters.ENVIRONMENT >>
    steps:
      - checkout
      - run: 
          command: |
              Start-Process powershell -verb runAs -Args "-start GeneralProfile"
              nvm install 18.17.1
              nvm use 18.17.1
          shell: powershell.exe
      # look for existing cache and restore if found
      - restore_cache:
          key: v1-deps-{{ checksum "package-lock.json" }}
      # python install
      - run:
          name: pip install
          command: pip install setuptools
      # install dependencies    
      - run:
          name: install dependencies
          command: npm install
      # save any changes to the cache
      - save_cache:
          key: v1-deps-{{ checksum "package-lock.json" }}
          paths: 
            - node_modules  
      - run:
          name: package and make electron windows
          command: |
           echo $WIN_CERTIFICATE_BASE64 | base64 --decode >./bryzoscert.pfx
           node_modules/cross-env/src/bin/cross-env.js WIN_CERTIFICATE_PASSWORD=$WIN_CERTIFICATE_PASSWORD NODE_ENV=$ENVIRONMENT npm run build:electron:circleci
      - run:
          name: upload files to drive
          command: |
            cd ./scripts/nodeUploader
            echo $DRIVE_CONFIG_BASE64 | base64 --decode >./config.json
            npm i
            node_modules/cross-env/src/bin/cross-env.js NODE_ENV=$ENVIRONMENT DRIVE_FOLDERID=${DRIVE_FOLDERID} npm run upload

      - store_artifacts:
          path: out/make


parameters:
  ENVIRONMENT:
    type: enum
    enum: [staging, qa, demo, production, csw]
    default: staging
  RUN_PIPELINE:
    type: boolean
    default: false

workflows:

  electron-build:
    when:
      and:
        - << pipeline.parameters.RUN_PIPELINE >>
        - << pipeline.parameters.ENVIRONMENT >>
    jobs:
      - build-macos
      - build-win